import { defineConfig } from 'vitest/config';
import { svelte } from '@sveltejs/vite-plugin-svelte';

export default defineConfig({
  plugins: [
    svelte({
      compilerOptions: {
        compatibility: {
          componentApi: 4
        }
      },
      hot: false
    })
  ],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./tests/setup.ts'],
    include: ['tests/**/*.test.ts', 'tests/**/*.test.js'],
    exclude: ['e2e/**/*'],
    testTimeout: 30000, // 30 seconds for unit tests
    hookTimeout: 10000,  // 10 seconds for setup/teardown hooks
  },
  resolve: {
    alias: {
      'obsidian': new URL('./tests/__mocks__/obsidian.ts', import.meta.url).pathname
    },
    conditions: ['browser']
  },
  define: {
    global: 'globalThis'
  }
});
