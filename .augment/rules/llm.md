---
type: "always_apply"
---

# Rules

- NEVER EVER delete e2e/test_obsidian_data

# Testing

- Always make sure that all tests are passing using `npm run test`
- Always make sure that all e2e tests are passing using `npm run test:e2e`
- To run a specific e2e test file use `npx vitest run --run e2e/specs/sync-content-issues.e2e.ts`
- The e2e tests MUST NOT try to publish any posts
- The e2e tests MUST NOT try to send any posts via newsletter
- Always check if there are existing files where new tests could be added instead of creating new files
