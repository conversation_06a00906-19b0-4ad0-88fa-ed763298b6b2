import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TestPostManager } from '../../e2e/helpers/ghost-api-helpers';
import matter from 'gray-matter';

// Mock the ghost-cleanup module
vi.mock('../../e2e/helpers/ghost-cleanup', () => ({
  createGhostAPIClient: vi.fn(() => ({
    createPost: vi.fn(),
    deletePost: vi.fn(),
    getPosts: vi.fn(),
    getPostBySlug: vi.fn(),
    updatePost: vi.fn()
  }))
}));

// Mock the ContentConverter
vi.mock('../../src/utils/content-converter', () => ({
  ContentConverter: {
    createGhostPostData: vi.fn()
  }
}));

// Mock fs and path modules
vi.mock('fs', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...(actual as object),
    readFileSync: vi.fn()
  };
});

vi.mock('path', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...(actual as object),
    resolve: vi.fn()
  };
});

describe('TestPostManager', () => {
  let postManager: TestPostManager;
  let mockGhostAPI: any;

  beforeEach(() => {
    postManager = new TestPostManager();
    mockGhostAPI = (postManager as any).ghostAPI;
  });

  describe('createPostFromFixture', () => {
    it('should parse frontmatter and create Ghost post using gray-matter', async () => {
      // Mock fs and path
      const fs = await import('fs');
      const path = await import('path');

      const mockFixtureContent = `---
title: Test Fixture Post
slug: test-fixture-post
status: draft
featured: false
visibility: public
---

# Test Fixture Content

This is test content from a fixture.
`;

      vi.mocked(path.resolve).mockReturnValue('/mocked/path/to/fixture.md');
      vi.mocked(fs.readFileSync).mockReturnValue(mockFixtureContent);

      // Mock ContentConverter.createGhostPostData
      const { ContentConverter } = await import('../../src/utils/content-converter');
      const createGhostPostDataSpy = vi.mocked(ContentConverter.createGhostPostData);
      createGhostPostDataSpy.mockResolvedValue({
        title: 'Test Fixture Post',
        slug: 'test-fixture-post',
        status: 'draft',
        featured: false,
        lexical: JSON.stringify({ root: { children: [] } })
      });

      // Mock Ghost API createPost
      const mockCreatedPost = { id: 'test-post-id', title: 'Test Fixture Post' };
      mockGhostAPI.createPost.mockResolvedValue(mockCreatedPost);

      // Call the method
      const result = await postManager.createPostFromFixture('test-fixture.md');

      // Verify ContentConverter.createGhostPostData was called with parsed data
      expect(createGhostPostDataSpy).toHaveBeenCalledWith(
        {
          title: 'Test Fixture Post',
          slug: 'test-fixture-post',
          status: 'draft',
          featured: false,
          visibility: 'public'
        },
        expect.stringContaining('# Test Fixture Content')
      );

      // Verify Ghost API createPost was called
      expect(mockGhostAPI.createPost).toHaveBeenCalled();

      // Verify result
      expect(result).toEqual(mockCreatedPost);

      // Verify post ID was tracked for cleanup
      expect((postManager as any).managedPostIds.has('test-post-id')).toBe(true);
    });

    it('should throw error if Ghost API is not available', async () => {
      // Create a post manager without Ghost API
      const postManagerWithoutAPI = new TestPostManager();
      (postManagerWithoutAPI as any).ghostAPI = null;

      // Call the method and expect it to throw
      await expect(postManagerWithoutAPI.createPostFromFixture('test-fixture.md'))
        .rejects.toThrow('Ghost API not available');
    });
  });

  describe('gray-matter integration', () => {
    it('should correctly parse frontmatter using gray-matter', () => {
      const testContent = `---
title: Test Post
slug: test-post
status: draft
featured: false
visibility: public
---

# Test Content

This is a test post with **bold** text and *italic* text.
`;

      const parsed = matter(testContent);

      expect(parsed.data).toEqual({
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        visibility: 'public'
      });

      expect(parsed.content.trim()).toBe('# Test Content\n\nThis is a test post with **bold** text and *italic* text.');
    });

    it('should handle frontmatter with various data types', () => {
      const testContent = `---
title: "Complex Post"
slug: complex-post
status: published
featured: true
tags:
  - javascript
  - testing
created_at: 2024-01-01T10:00:00.000Z
---

Content here.
`;

      const parsed = matter(testContent);

      expect(parsed.data.title).toBe('Complex Post');
      expect(parsed.data.featured).toBe(true);
      expect(parsed.data.tags).toEqual(['javascript', 'testing']);
      expect(parsed.data.created_at).toBeInstanceOf(Date);
    });
  });
});
