import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { TFile } from 'obsidian';
import type { GhostPost } from '../../src/types';

// Mock the sync status view classes
class MockSyncStatusView {
  currentFile: TFile | null = null;
  syncStatus: any = {};
  plugin: any = {};
  app: any = {};

  constructor() {
    this.plugin = {
      settings: { verbose: false },
      syncCurrentPostToGhost: vi.fn().mockResolvedValue(undefined)
    };
  }

  async syncFromGhost() {
    // Mock implementation
    return Promise.resolve();
  }

  updateSyncStatus() {
    // Mock implementation
  }

  // Test the smart sync logic directly
  async performSmartSync() {
    if (!this.currentFile || !this.syncStatus.ghostPost) {
      throw new Error('No Ghost post to sync');
    }

    const ghostPost = this.syncStatus.ghostPost;

    // Mock reading file content and parsing frontmatter
    const mockContent = `---
title: Test Post
updated_at: ${this.mockLocalUpdatedAt || '2024-01-01T00:00:00.000Z'}
---

Test content`;

    // Simulate parsing frontmatter
    const localUpdatedAt = this.mockLocalUpdatedAt || '2024-01-01T00:00:00.000Z';
    const fileModificationTime = this.currentFile.stat?.mtime || 0;
    const ghostUpdatedAtMs = new Date(ghostPost.updated_at).getTime();
    const localUpdatedAtMs = new Date(localUpdatedAt).getTime();

    if (this.plugin.settings.verbose) {
      console.log('Smart sync comparison:');
      console.log('Ghost updated_at:', ghostPost.updated_at);
      console.log('Local frontmatter updated_at:', localUpdatedAt);
      console.log('File modification time:', new Date(fileModificationTime).toISOString());
      console.log('Ghost vs Local updated_at diff (ms):', ghostUpdatedAtMs - localUpdatedAtMs);
      console.log('File vs Local updated_at diff (ms):', fileModificationTime - localUpdatedAtMs);
    }

    // Return the sync decision for testing
    if (ghostPost.status !== 'draft') {
      return 'sync_from_ghost_published';
    }

    // Check if Ghost has been updated since our last sync TO Ghost
    const ghostIsNewer = ghostUpdatedAtMs > localUpdatedAtMs;

    // Check if file has been modified since our last sync TO Ghost (with tolerance)
    const fileIsNewer = fileModificationTime > (localUpdatedAtMs + 1000); // 1 second tolerance

    if (ghostIsNewer && fileIsNewer) {
      return 'bidirectional_sync';
    }
    else if (ghostIsNewer) {
      return 'sync_from_ghost_newer';
    }
    else if (fileIsNewer) {
      return 'sync_to_ghost_newer';
    }
    else {
      return 'no_changes';
    }
  }

  // Helper method to set mock local updated_at for testing
  setMockLocalUpdatedAt(updatedAt: string) {
    this.mockLocalUpdatedAt = updatedAt;
  }

  private mockLocalUpdatedAt?: string;
}

describe('Timestamp-based Sync Logic', () => {
  let syncView: MockSyncStatusView;
  let mockFile: TFile;
  let mockGhostPost: GhostPost;

  beforeEach(() => {
    vi.clearAllMocks();
    syncView = new MockSyncStatusView();

    // Create mock file with stat
    mockFile = {
      stat: {
        mtime: Date.now(),
        ctime: Date.now(),
        size: 1000
      },
      path: 'test.md',
      name: 'test.md',
      basename: 'test',
      extension: 'md'
    } as TFile;

    // Create mock Ghost post
    mockGhostPost = {
      id: 'ghost-id',
      title: 'Test Post',
      slug: 'test-post',
      status: 'draft',
      updated_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      published_at: null,
      html: '<p>Test content</p>',
      lexical: null,
      tags: [],
      authors: [],
      featured: false,
      feature_image: null,
      visibility: 'public',
      email: null
    } as GhostPost;

    syncView.currentFile = mockFile;
    syncView.syncStatus = { ghostPost: mockGhostPost };
  });

  describe('Published Post Priority', () => {
    it('should sync from Ghost when post is published', async () => {
      mockGhostPost.status = 'published';

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_from_ghost_published');
    });

    it('should sync from Ghost when post is published', async () => {
      mockGhostPost.status = 'published';

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_from_ghost_published');
    });
  });

  describe('Timestamp Comparison for Drafts', () => {
    it('should sync from Ghost when Ghost is newer than local updated_at', async () => {
      const baseTime = Date.now();

      // Local updated_at (last sync TO Ghost) was 2 hours ago
      const localUpdatedAt = new Date(baseTime - (2 * 60 * 60 * 1000)).toISOString();
      syncView.setMockLocalUpdatedAt(localUpdatedAt);

      // Ghost updated_at is 1 hour ago (newer than local updated_at)
      mockGhostPost.updated_at = new Date(baseTime - (60 * 60 * 1000)).toISOString();

      // File modification time is old (before local updated_at, so no local changes)
      mockFile.stat!.mtime = baseTime - (3 * 60 * 60 * 1000);

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_from_ghost_newer');
    });

    it('should sync to Ghost when file is newer than local updated_at', async () => {
      const baseTime = Date.now();

      // Local updated_at (last sync TO Ghost) was 2 hours ago
      const localUpdatedAt = new Date(baseTime - (2 * 60 * 60 * 1000)).toISOString();
      syncView.setMockLocalUpdatedAt(localUpdatedAt);

      // Ghost updated_at is same as local (no changes in Ghost)
      mockGhostPost.updated_at = localUpdatedAt;

      // File was modified 1 hour ago (after local updated_at)
      mockFile.stat!.mtime = baseTime - (60 * 60 * 1000);

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_to_ghost_newer');
    });

    it('should do bidirectional sync when both Ghost and file are newer', async () => {
      const baseTime = Date.now();

      // Local updated_at (last sync TO Ghost) was 3 hours ago
      const localUpdatedAt = new Date(baseTime - (3 * 60 * 60 * 1000)).toISOString();
      syncView.setMockLocalUpdatedAt(localUpdatedAt);

      // Ghost updated_at is 2 hours ago (newer than local updated_at)
      mockGhostPost.updated_at = new Date(baseTime - (2 * 60 * 60 * 1000)).toISOString();

      // File was modified 1 hour ago (also newer than local updated_at)
      mockFile.stat!.mtime = baseTime - (60 * 60 * 1000);

      const result = await syncView.performSmartSync();

      expect(result).toBe('bidirectional_sync');
    });

    it('should report no changes when neither Ghost nor file are newer', async () => {
      const baseTime = Date.now();

      // Local updated_at (last sync TO Ghost) was 1 hour ago
      const localUpdatedAt = new Date(baseTime - (60 * 60 * 1000)).toISOString();
      syncView.setMockLocalUpdatedAt(localUpdatedAt);

      // Ghost updated_at is same as local (no changes in Ghost)
      mockGhostPost.updated_at = localUpdatedAt;

      // File was modified before local updated_at (no local changes)
      mockFile.stat!.mtime = baseTime - (2 * 60 * 60 * 1000);

      const result = await syncView.performSmartSync();

      expect(result).toBe('no_changes');
    });
  });

  describe('Edge Cases', () => {
    it('should handle missing file stat', async () => {
      mockFile.stat = undefined as any;

      const baseTime = Date.now();
      const localUpdatedAt = new Date(baseTime - (60 * 60 * 1000)).toISOString();
      syncView.setMockLocalUpdatedAt(localUpdatedAt);

      // Ghost is newer than local updated_at
      mockGhostPost.updated_at = new Date().toISOString();

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_from_ghost_newer');
    });

    it('should handle invalid Ghost updated_at', async () => {
      mockGhostPost.updated_at = 'invalid-date';

      const baseTime = Date.now();
      const localUpdatedAt = new Date(baseTime - (60 * 60 * 1000)).toISOString();
      syncView.setMockLocalUpdatedAt(localUpdatedAt);

      // File was modified recently
      mockFile.stat!.mtime = baseTime;

      const result = await syncView.performSmartSync();

      // With invalid Ghost date, file should appear newer
      expect(result).toBe('sync_to_ghost_newer');
    });

    it('should throw error when no current file', async () => {
      syncView.currentFile = null;

      await expect(syncView.performSmartSync()).rejects.toThrow('No Ghost post to sync');
    });

    it('should throw error when no Ghost post', async () => {
      syncView.syncStatus.ghostPost = null;

      await expect(syncView.performSmartSync()).rejects.toThrow('No Ghost post to sync');
    });
  });

  describe('Verbose Logging', () => {
    it('should log comparison details when verbose is enabled', async () => {
      syncView.plugin.settings.verbose = true;
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      await syncView.performSmartSync();

      expect(consoleSpy).toHaveBeenCalledWith('Smart sync comparison:');
      expect(consoleSpy).toHaveBeenCalledWith('Ghost updated_at:', expect.any(String));
      expect(consoleSpy).toHaveBeenCalledWith('Local frontmatter updated_at:', expect.any(String));
      expect(consoleSpy).toHaveBeenCalledWith('File modification time:', expect.any(String));
      expect(consoleSpy).toHaveBeenCalledWith('Ghost vs Local updated_at diff (ms):', expect.any(Number));
      expect(consoleSpy).toHaveBeenCalledWith('File vs Local updated_at diff (ms):', expect.any(Number));

      consoleSpy.mockRestore();
    });

    it('should not log when verbose is disabled', async () => {
      syncView.plugin.settings.verbose = false;
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      await syncView.performSmartSync();

      expect(consoleSpy).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle typical editing workflow', async () => {
      // Scenario: User edits file locally after last sync to Ghost
      const baseTime = Date.now();

      // Last sync TO Ghost was 30 minutes ago
      const localUpdatedAt = new Date(baseTime - (30 * 60 * 1000)).toISOString();
      syncView.setMockLocalUpdatedAt(localUpdatedAt);

      // Ghost hasn't been updated since then
      mockGhostPost.updated_at = localUpdatedAt;

      // File was modified 10 minutes ago (after last sync to Ghost)
      mockFile.stat!.mtime = baseTime - (10 * 60 * 1000);

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_to_ghost_newer');
    });

    it('should handle Ghost CMS editing workflow', async () => {
      // Scenario: User edits post in Ghost CMS after last sync
      const baseTime = Date.now();

      // Last sync TO Ghost was 30 minutes ago
      const localUpdatedAt = new Date(baseTime - (30 * 60 * 1000)).toISOString();
      syncView.setMockLocalUpdatedAt(localUpdatedAt);

      // Ghost was updated 10 minutes ago (after last sync to Ghost)
      mockGhostPost.updated_at = new Date(baseTime - (10 * 60 * 1000)).toISOString();

      // File hasn't been modified since last sync
      mockFile.stat!.mtime = baseTime - (40 * 60 * 1000);

      const result = await syncView.performSmartSync();

      expect(result).toBe('sync_from_ghost_newer');
    });

    it('should handle conflict scenario (both sides edited)', async () => {
      // Scenario: Both Ghost and local file have been edited since last sync
      const baseTime = Date.now();

      // Last sync TO Ghost was 60 minutes ago
      const localUpdatedAt = new Date(baseTime - (60 * 60 * 1000)).toISOString();
      syncView.setMockLocalUpdatedAt(localUpdatedAt);

      // Ghost was updated 30 minutes ago (after last sync)
      mockGhostPost.updated_at = new Date(baseTime - (30 * 60 * 1000)).toISOString();

      // File was modified 20 minutes ago (also after last sync)
      mockFile.stat!.mtime = baseTime - (20 * 60 * 1000);

      const result = await syncView.performSmartSync();

      // Should do bidirectional sync for conflict resolution
      expect(result).toBe('bidirectional_sync');
    });

    it('should handle fresh sync scenario', async () => {
      // Scenario: First time syncing a post (no local updated_at)
      const baseTime = Date.now();

      // No local updated_at (fresh file) - use a recent time to avoid file appearing newer
      const recentTime = baseTime - (30 * 60 * 1000); // 30 minutes ago
      syncView.setMockLocalUpdatedAt(new Date(recentTime).toISOString());

      // Ghost was updated after the local updated_at
      mockGhostPost.updated_at = new Date(baseTime - (10 * 60 * 1000)).toISOString();

      // File was not modified since local updated_at (no local changes)
      mockFile.stat!.mtime = recentTime - (5 * 60 * 1000); // Before local updated_at

      const result = await syncView.performSmartSync();

      // Ghost should be considered newer
      expect(result).toBe('sync_from_ghost_newer');
    });

    it('should handle tolerance edge case', async () => {
      // Scenario: File modified just within tolerance of last sync
      const baseTime = Date.now();

      // Last sync TO Ghost was 10 minutes ago
      const localUpdatedAt = new Date(baseTime - (10 * 60 * 1000)).toISOString();
      syncView.setMockLocalUpdatedAt(localUpdatedAt);

      // Ghost hasn't been updated since
      mockGhostPost.updated_at = localUpdatedAt;

      // File was modified just 500ms after last sync (within 1000ms tolerance)
      mockFile.stat!.mtime = baseTime - (10 * 60 * 1000) + 500;

      const result = await syncView.performSmartSync();

      // Should be considered no changes due to tolerance
      expect(result).toBe('no_changes');
    });
  });
});
