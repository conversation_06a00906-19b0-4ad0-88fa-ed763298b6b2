import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ObsidianGhostAPI } from '../../src/api/ghost-api';

// Mock fetch globally
global.fetch = vi.fn();

// Mock the Ghost Admin API
vi.mock('@tryghost/admin-api', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      posts: {
        browse: vi.fn().mockResolvedValue([
          { id: '1', title: 'Test Post', slug: 'test-post' },
          { id: '2', title: 'Another Post', slug: 'another-post' }
        ]),
        read: vi.fn().mockResolvedValue({ id: '1', title: 'Test Post', slug: 'test-post' }),
        add: vi.fn().mockResolvedValue({ id: '3', title: 'New Post', slug: 'new-post' }),
        edit: vi.fn().mockResolvedValue({ id: '1', title: 'Updated Post', slug: 'test-post' }),
        delete: vi.fn().mockResolvedValue(undefined)
      },
      newsletters: {
        browse: vi.fn().mockResolvedValue([
          { id: '1', name: 'Test Newsletter', slug: 'test-newsletter' },
          { id: '2', name: 'Another Newsletter', slug: 'another-newsletter' }
        ])
      },
      users: {
        browse: vi.fn().mockResolvedValue([
          { id: '1', name: 'Test User', email: '<EMAIL>' }
        ])
      }
    }))
  };
});

describe('GhostAPI', () => {
  let ghostAPI: ObsidianGhostAPI;
  const mockSettings = {
    ghostUrl: 'https://test.ghost.io',
    ghostAdminApiKey: '123456789012345678901234:1234567890123456789012345678901234567890123456789012345678901234',
    ghostContentApiKey: 'test-content-key'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    ghostAPI = new ObsidianGhostAPI(mockSettings.ghostUrl, mockSettings.ghostAdminApiKey);
  });

  describe('Constructor', () => {
    it('should create instance with valid settings', () => {
      expect(ghostAPI).toBeInstanceOf(ObsidianGhostAPI);
    });

    it('should handle missing settings gracefully', () => {
      expect(() => {
        new ObsidianGhostAPI('https://test.ghost.io', '123456789012345678901234:1234567890123456789012345678901234567890123456789012345678901234');
      }).not.toThrow();
    });
  });

  describe('API Methods', () => {
    it('should have getPosts method', () => {
      expect(typeof ghostAPI.getPosts).toBe('function');
    });

    it('should have getPostBySlug method', () => {
      expect(typeof ghostAPI.getPostBySlug).toBe('function');
    });

    it('should have createPost method', () => {
      expect(typeof ghostAPI.createPost).toBe('function');
    });

    it('should have updatePost method', () => {
      expect(typeof ghostAPI.updatePost).toBe('function');
    });

    it('should have getNewsletters method', () => {
      expect(typeof ghostAPI.getNewsletters).toBe('function');
    });

    it('should have deletePost method', () => {
      expect(typeof ghostAPI.deletePost).toBe('function');
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      // Mock the API to throw a network error
      const mockAPI = {
        posts: {
          browse: vi.fn().mockRejectedValue(new Error('Network error'))
        }
      };

      // Replace the API instance
      (ghostAPI as any).api = mockAPI;

      await expect(ghostAPI.getPosts()).rejects.toThrow('Network error');
    });

    it('should handle invalid JSON responses', async () => {
      // Mock the API to throw a JSON parsing error
      const mockAPI = {
        posts: {
          browse: vi.fn().mockRejectedValue(new Error('Invalid JSON'))
        }
      };

      // Replace the API instance
      (ghostAPI as any).api = mockAPI;

      await expect(ghostAPI.getPosts()).rejects.toThrow('Invalid JSON');
    });

    it('should handle HTTP error responses', async () => {
      // Mock the API to throw an HTTP error
      const mockAPI = {
        posts: {
          browse: vi.fn().mockRejectedValue(new Error('Not found'))
        }
      };

      // Replace the API instance
      (ghostAPI as any).api = mockAPI;

      await expect(ghostAPI.getPosts()).rejects.toThrow('Not found');
    });
  });

  describe('Authentication', () => {
    it('should include proper headers for admin API calls', async () => {
      const result = await ghostAPI.getPosts();

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });

    it('should include admin auth headers in API calls', async () => {
      // Test that the admin API is called with proper authentication
      const result = await ghostAPI.getPosts();

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe('Data Transformation', () => {
    it('should properly format post data for creation', async () => {
      const mockPost = {
        title: 'Test Post',
        slug: 'test-post',
        html: '<p>Test content</p>',
        status: 'draft',
        visibility: 'public'
      };

      const result = await ghostAPI.createPost(mockPost);

      expect(result).toBeDefined();
      expect(result.title).toBe('New Post');
    });

    it('should handle post updates correctly', async () => {
      const mockPost = {
        id: 'test-id',
        title: 'Updated Post',
        slug: 'updated-post',
        html: '<p>Updated content</p>',
        updated_at: '2023-01-01T00:00:00.000Z'
      };

      const result = await ghostAPI.updatePost(mockPost);

      expect(result).toBeDefined();
      expect(result.title).toBe('Updated Post');
    });
  });

  describe('Newsletter Operations', () => {
    it('should fetch newsletters successfully', async () => {
      const result = await ghostAPI.getNewsletters();

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(2);
      expect(result[0]).toHaveProperty('id');
      expect(result[0]).toHaveProperty('name');
      expect(result[0]).toHaveProperty('slug');
    });

    it('should handle empty newsletter response', async () => {
      // Mock the API to return empty newsletters
      const mockAPI = {
        newsletters: {
          browse: vi.fn().mockResolvedValue([])
        }
      };

      // Replace the API instance
      (ghostAPI as any).api = mockAPI;

      const result = await ghostAPI.getNewsletters();

      expect(result).toEqual([]);
    });
  });

  describe('Post Deletion', () => {
    it('should delete a post successfully', async () => {
      const postId = 'test-post-id';

      await expect(ghostAPI.deletePost(postId)).resolves.toBeUndefined();
    });

    it('should handle deletion errors gracefully', async () => {
      // Mock the API to throw an error
      const mockAPI = {
        posts: {
          delete: vi.fn().mockRejectedValue(new Error('Post not found'))
        }
      };

      // Replace the API instance
      (ghostAPI as any).api = mockAPI;

      await expect(ghostAPI.deletePost('non-existent-id')).rejects.toThrow('Post not found');
    });
  });

  describe('Rate Limiting', () => {
    it('should handle rate limit responses', async () => {
      // Mock the Ghost Admin API to throw an error
      const mockAPI = {
        posts: {
          browse: vi.fn().mockRejectedValue(new Error('Rate limit exceeded'))
        }
      };

      // Replace the API instance
      (ghostAPI as any).api = mockAPI;

      await expect(ghostAPI.getPosts()).rejects.toThrow('Rate limit exceeded');
    });
  });

  describe('Pagination', () => {
    it('should have getAllPosts method', () => {
      expect(typeof ghostAPI.getAllPosts).toBe('function');
    });

    it('should fetch all posts with pagination when there are multiple pages', async () => {
      // Mock multiple pages of posts
      const page1Posts = Array.from({ length: 100 }, (_, i) => ({
        id: `${i + 1}`,
        title: `Post ${i + 1}`,
        slug: `post-${i + 1}`
      }));

      const page2Posts = Array.from({ length: 50 }, (_, i) => ({
        id: `${i + 101}`,
        title: `Post ${i + 101}`,
        slug: `post-${i + 101}`
      }));

      const mockAPI = {
        posts: {
          browse: vi.fn()
            .mockResolvedValueOnce(Object.assign(page1Posts, {
              meta: { pagination: { next: 2 } }
            }))
            .mockResolvedValueOnce(Object.assign(page2Posts, {
              meta: { pagination: { next: null } }
            }))
        }
      };

      // Replace the API instance
      (ghostAPI as any).api = mockAPI;

      const result = await ghostAPI.getAllPosts();

      expect(result).toHaveLength(150);
      expect(mockAPI.posts.browse).toHaveBeenCalledTimes(2);
      expect(mockAPI.posts.browse).toHaveBeenNthCalledWith(1, { limit: 100, page: 1, formats: 'html,lexical', include: 'tags,authors' });
      expect(mockAPI.posts.browse).toHaveBeenNthCalledWith(2, { limit: 100, page: 2, formats: 'html,lexical', include: 'tags,authors' });
    });

    it('should fetch all posts with pagination when there is only one page', async () => {
      const posts = Array.from({ length: 50 }, (_, i) => ({
        id: `${i + 1}`,
        title: `Post ${i + 1}`,
        slug: `post-${i + 1}`
      }));

      const mockAPI = {
        posts: {
          browse: vi.fn().mockResolvedValue(Object.assign(posts, {
            meta: { pagination: { next: null } }
          }))
        }
      };

      // Replace the API instance
      (ghostAPI as any).api = mockAPI;

      const result = await ghostAPI.getAllPosts();

      expect(result).toHaveLength(50);
      expect(mockAPI.posts.browse).toHaveBeenCalledTimes(1);
      expect(mockAPI.posts.browse).toHaveBeenCalledWith({ limit: 100, page: 1, formats: 'html,lexical', include: 'tags,authors' });
    });

    it('should handle empty result set', async () => {
      const mockAPI = {
        posts: {
          browse: vi.fn().mockResolvedValue([])
        }
      };

      // Replace the API instance
      (ghostAPI as any).api = mockAPI;

      const result = await ghostAPI.getAllPosts();

      expect(result).toHaveLength(0);
      expect(mockAPI.posts.browse).toHaveBeenCalledTimes(1);
    });

    it('should pass through options to pagination requests', async () => {
      const posts = Array.from({ length: 10 }, (_, i) => ({
        id: `${i + 1}`,
        title: `Post ${i + 1}`,
        slug: `post-${i + 1}`
      }));

      const mockAPI = {
        posts: {
          browse: vi.fn().mockResolvedValue(Object.assign(posts, {
            meta: { pagination: { next: null } }
          }))
        }
      };

      // Replace the API instance
      (ghostAPI as any).api = mockAPI;

      const options = { include: 'tags,authors', filter: 'status:published' };
      const result = await ghostAPI.getAllPosts(options);

      expect(result).toHaveLength(10);
      expect(mockAPI.posts.browse).toHaveBeenCalledWith({
        ...options,
        limit: 100,
        page: 1,
        formats: 'html,lexical',
        include: 'tags,authors'
      });
    });

    it('should handle pagination fallback when meta is not available', async () => {
      const page1Posts = Array.from({ length: 100 }, (_, i) => ({
        id: `${i + 1}`,
        title: `Post ${i + 1}`,
        slug: `post-${i + 1}`
      }));

      const page2Posts = Array.from({ length: 50 }, (_, i) => ({
        id: `${i + 101}`,
        title: `Post ${i + 101}`,
        slug: `post-${i + 101}`
      }));

      const mockAPI = {
        posts: {
          browse: vi.fn()
            .mockResolvedValueOnce(page1Posts) // No meta property
            .mockResolvedValueOnce(page2Posts) // No meta property
        }
      };

      // Replace the API instance
      (ghostAPI as any).api = mockAPI;

      const result = await ghostAPI.getAllPosts();

      expect(result).toHaveLength(150);
      expect(mockAPI.posts.browse).toHaveBeenCalledTimes(2);
    });

    it('should handle errors during pagination', async () => {
      const mockAPI = {
        posts: {
          browse: vi.fn().mockRejectedValue(new Error('Pagination error'))
        }
      };

      // Replace the API instance
      (ghostAPI as any).api = mockAPI;

      await expect(ghostAPI.getAllPosts()).rejects.toThrow('Pagination error');
    });
  });
});
