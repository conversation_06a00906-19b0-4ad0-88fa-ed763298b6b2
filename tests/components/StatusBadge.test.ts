import { mount, unmount } from 'svelte';
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import StatusBadge from '../../src/components/StatusBadge.svelte';
import { createMockContainer, cleanupContainer } from '../test-utils';

describe('StatusBadge Component', () => {
  let container: HTMLElement;
  let component: any;

  beforeEach(() => {
    container = createMockContainer();
  });

  afterEach(() => {
    if (component) {
      unmount(component);
      component = null;
    }
    cleanupContainer(container);
  });

  describe('Rendering', () => {
    it('should render without crashing', () => {
      expect(() => {
        component = mount(StatusBadge, {
          target: container,
          props: {
            status: 'synced'
          }
        });
      }).not.toThrow();
    });

    it('should display synced status', () => {
      component = mount(StatusBadge, {
        target: container,
        props: {
          status: 'synced'
        }
      });

      expect(container.textContent).toContain('Synced');
    });

    it('should display different status', () => {
      component = mount(StatusBadge, {
        target: container,
        props: {
          status: 'different'
        }
      });

      expect(container.textContent).toContain('Different');
    });

    it('should display new post status', () => {
      component = mount(StatusBadge, {
        target: container,
        props: {
          status: 'new_post'
        }
      });

      expect(container.textContent).toContain('New');
    });

    it('should display unknown status', () => {
      component = mount(StatusBadge, {
        target: container,
        props: {
          status: 'unknown'
        }
      });

      expect(container.textContent).toContain('Unknown');
    });
  });

  describe('Status Types', () => {
    it('should handle all valid status types', () => {
      const statuses = ['synced', 'different', 'unknown'] as const;

      statuses.forEach(status => {
        expect(() => {
          component = mount(StatusBadge, {
            target: container,
            props: { status }
          });
          unmount(component);
        }).not.toThrow();
      });
    });

    it('should apply appropriate CSS classes for different statuses', () => {
      // Test synced status
      component = mount(StatusBadge, {
        target: container,
        props: {
          status: 'synced'
        }
      });

      const badge = container.querySelector('.ghost-sync-status-badge');
      expect(badge).toBeTruthy();

      unmount(component);

      // Test different status
      component = mount(StatusBadge, {
        target: container,
        props: {
          status: 'different'
        }
      });

      const differentBadge = container.querySelector('.ghost-sync-status-badge');
      expect(differentBadge).toBeTruthy();
    });
  });

  describe('Props Updates', () => {
    it('should update when status changes', () => {
      component = mount(StatusBadge, {
        target: container,
        props: {
          status: 'synced'
        }
      });

      expect(container.textContent).toContain('Synced');

      component.$set({
        status: 'different'
      });

      expect(container.textContent).toContain('Different');
    });

    it('should handle rapid status changes', () => {
      component = mount(StatusBadge, {
        target: container,
        props: {
          status: 'synced'
        }
      });

      const statuses = [
        { status: 'different', expected: 'Different' },
        { status: 'unknown', expected: 'Unknown' },
        { status: 'synced', expected: 'Synced' },
        { status: 'different', expected: 'Different' }
      ];

      statuses.forEach(({ status, expected }) => {
        component.$set({ status });
        expect(container.textContent).toContain(expected);
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined status gracefully', () => {
      expect(() => {
        component = mount(StatusBadge, {
          target: container,
          props: {
            status: undefined as any
          }
        });
      }).not.toThrow();
    });

    it('should handle null status gracefully', () => {
      expect(() => {
        component = mount(StatusBadge, {
          target: container,
          props: {
            status: null as any
          }
        });
      }).not.toThrow();
    });

    it('should handle empty string status', () => {
      expect(() => {
        component = mount(StatusBadge, {
          target: container,
          props: {
            status: '' as any
          }
        });
      }).not.toThrow();
    });
  });

  describe('Accessibility', () => {
    it('should have appropriate ARIA attributes', () => {
      component = mount(StatusBadge, {
        target: container,
        props: {
          status: 'synced'
        }
      });

      const badge = container.querySelector('.ghost-sync-status-badge');
      expect(badge).toBeTruthy();

      // Check if the badge has some accessibility considerations
      expect(badge?.textContent).toBeTruthy();
    });

    it('should be readable by screen readers', () => {
      component = mount(StatusBadge, {
        target: container,
        props: {
          status: 'different'
        }
      });

      // The component should have meaningful text content
      expect(container.textContent?.trim()).toBeTruthy();
      expect(container.textContent?.trim()).not.toBe('');
    });
  });
});
