import { mount, unmount } from 'svelte';
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import PropertyDisplay from '../../src/components/PropertyDisplay.svelte';
import { createMockContainer, cleanupContainer } from '../test-utils';

describe('PropertyDisplay Component', () => {
  let container: HTMLElement;
  let component: any;

  beforeEach(() => {
    container = createMockContainer();
  });

  afterEach(() => {
    if (component) {
      unmount(component);
      component = null;
    }
    cleanupContainer(container);
  });

  describe('Rendering', () => {
    it('should render without crashing', () => {
      expect(() => {
        component = mount(PropertyDisplay, {
          target: container,
          props: {
            label: 'Test Label',
            localValue: 'local',
            ghostValue: 'ghost'
          }
        });
      }).not.toThrow();
    });

    it('should display the label', () => {
      component = mount(PropertyDisplay, {
        target: container,
        props: {
          label: 'Title',
          localValue: 'local',
          ghostValue: 'ghost'
        }
      });

      expect(container.textContent).toContain('Title');
    });

    it('should display local and ghost values', () => {
      component = mount(PropertyDisplay, {
        target: container,
        props: {
          label: 'Status',
          status: 'different',
          value: 'draft'
        }
      });

      expect(container.textContent).toContain('draft');
      expect(container.textContent).toContain('Status');
    });

    it('should handle empty values', () => {
      component = mount(PropertyDisplay, {
        target: container,
        props: {
          label: 'Tags',
          status: 'synced',
          value: ''
        }
      });

      expect(container.textContent).toContain('Tags');
    });

    it('should handle null values', () => {
      expect(() => {
        component = mount(PropertyDisplay, {
          target: container,
          props: {
            label: 'Feature Image',
            status: 'synced',
            value: null
          }
        });
      }).not.toThrow();
    });
  });

  describe('Status Indication', () => {
    it('should show synced status when values match', () => {
      component = mount(PropertyDisplay, {
        target: container,
        props: {
          label: 'Title',
          status: 'synced',
          value: 'same-value'
        }
      });

      expect(container.innerHTML).toBeTruthy();
    });

    it('should show different status when values differ', () => {
      component = mount(PropertyDisplay, {
        target: container,
        props: {
          label: 'Slug',
          status: 'different',
          value: 'local-slug'
        }
      });

      expect(container.innerHTML).toBeTruthy();
    });
  });

  describe('Props Updates', () => {
    it('should update when props change', () => {
      component = mount(PropertyDisplay, {
        target: container,
        props: {
          label: 'Title',
          status: 'synced',
          value: 'initial'
        }
      });

      expect(container.textContent).toContain('initial');

      component.$set({
        value: 'updated'
      });

      expect(container.textContent).toContain('updated');
    });
  });
});
