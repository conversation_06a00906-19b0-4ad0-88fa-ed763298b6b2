---
slug: sync-content-test
title: Sync Content Test
status: draft
featured: false
visibility: public
tags:
  - test
  - sync
  - content
---

# Sync Content Test

This is a test post for reproducing the frontmatter cache error when syncing from Ghost post browser.

## Test Content

This post contains basic content to test the sync functionality from Ghost to Obsidian without encountering frontmatter cache errors.

The sync process should handle this content properly even when the local file doesn't exist yet or doesn't have frontmatter in the metadata cache.

```javascript
function testSync() {
    console.log("Testing sync functionality");
    return "Sync successful";
}
```

## Key Points

- The post should sync successfully from Ghost
- No frontmatter cache errors should occur
- The content should be preserved correctly
- The file should be created in the articles directory

> [!info]
> This is an info callout

---

**End of test content.**
