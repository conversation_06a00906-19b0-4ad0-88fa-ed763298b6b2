---
slug: sync-all-test-post-2
title: Sync All Test Post 2 - Extended Formatting
status: published
featured: false
visibility: public
tags:
  - test
  - sync-all
  - formatting
---

# Sync All Test Post 2 - Extended Formatting

This second test post focuses on more complex formatting elements and longer content to thoroughly test the sync-all functionality.

## Rich Text Formatting

This paragraph demonstrates various inline formatting options: **bold text**, *italic text*, ***bold and italic***, ~~strikethrough text~~, and `inline code snippets`.

You can also combine formatting in interesting ways: **bold with *nested italic* text** and *italic with **nested bold** text*.

## Advanced Lists

### Task Lists

- [x] Completed task item
- [ ] Incomplete task item
- [x] Another completed task with **bold text**
- [ ] Incomplete task with *italic text*

### Nested Lists

1. First level item
   - Nested bullet point
   - Another nested item with **formatting**
   - Third nested item
2. Second level item
   1. Nested numbered item
   2. Another nested numbered item
   3. Third nested numbered item
3. Third level item

## Tables

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Row 1, Col 1 | **Bold content** | *Italic content* |
| Row 2, Col 1 | `Code content` | Regular content |
| Row 3, Col 1 | ~~Strikethrough~~ | Mixed **bold** and *italic* |

## Multiple Code Blocks

Here's a JavaScript example:

```javascript
const syncAllTest = {
    name: "Sync All Test",
    description: "Testing comprehensive content sync",
    execute: function() {
        return "Full content synchronization";
    }
};
```

And here's a Python example:

```python
def sync_all_test():
    """Test function for sync-all functionality"""
    content = "This is a comprehensive test"
    return f"Syncing: {content}"
```

## Extended Blockquotes

> This is a comprehensive blockquote that spans multiple lines.
> 
> It includes **bold text**, *italic text*, and `inline code`.
> 
> > This is a nested blockquote within the main blockquote.
> > It should also be preserved during synchronization.
> 
> The blockquote continues with more content to test the sync process.

## Links and References

Here are some example links:
- [External link](https://example.com)
- [Another external link](https://ghost.org)

## Conclusion

This extended test post contains substantial content across multiple sections, various formatting elements, and complex structures. The sync-all command should preserve all of this content exactly as it appears, ensuring that no information is lost or truncated during the synchronization process.

The test should verify that every paragraph, every formatting element, and every structural component is accurately transferred from Ghost to the local Obsidian vault.

**End of extended formatting test.**
