# Ghost Lexical Reference

This directory contains the reference Lexical structure from <PERSON> for testing our Markdown parser.

## Files

- `obsidian-ghost-formatting-test.json` - The complete Ghost post JSON containing all formatting elements we support, including the Lexical structure

## The Test Post

We maintain **one test post** in Ghost that contains all the formatting elements we want to support:

- **Title**: "Obsidian Ghost Sync - Formatting Test"
- **Slug**: `obsidian-ghost-formatting-test`
- **URL**: https://obsidian-plugin.ghost.io/ghost/#/editor/post/68a45b765f47170001b8c572

### Content includes:
- H1-H6 headings
- Text formatting: **bold** (format: 1), *italic* (format: 2), ***bold italic*** (format: 3), ~~strikethrough~~ (format: 8), `inline code` (format: 16)
- Lists: unordered and ordered with nesting
- All supported callouts: note, info, tip, success, warning, danger, error, question, quote, example, abstract, todo, bug
- Tables with headers and data
- Code blocks with and without syntax highlighting
- Links and references
- Proper `direction: "ltr"` values
- No extra `textFormat`/`textStyle` properties

## Managing the Test Post

Use the script to manage the test post:

```bash
# Create or update the test post
node scripts/ghost-formatting-test.js create

# Fetch the latest Lexical structure from Ghost
node scripts/ghost-formatting-test.js fetch

# Do both in one command
node scripts/ghost-formatting-test.js both

# Clean up old test posts
node scripts/ghost-formatting-test.js cleanup
```

## Using the Reference

Our Markdown parser should generate Lexical JSON that matches the structure in `obsidian-ghost-formatting-test.json`. The key requirements:

1. **Direction**: Use `"ltr"` not `null`
2. **Format flags**: 1=bold, 2=italic, 3=bold+italic, 8=strikethrough, 16=code
3. **Clean structure**: No extra `textFormat`/`textStyle` properties
4. **Proper nesting**: Text nodes inside paragraph/heading nodes

This reference ensures our parser generates Ghost-compatible Lexical JSON.
