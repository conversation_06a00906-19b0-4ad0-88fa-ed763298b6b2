{"id": "68a45b765f47170001b8c572", "uuid": "9f728743-1063-448a-b19b-29ec9254b87d", "title": "Obsidian Ghost Sync - Comprehensive Formatting Test", "slug": "obsidian-ghost-formatting-test", "lexical": "{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Obsidian Ghost Sync - Formatting Test\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h1\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"This post contains all formatting elements we want to support.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Text Formatting\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Here we test \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"bold text\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\", \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":2,\"mode\":\"normal\",\"style\":\"\",\"text\":\"italic text\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\", \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":3,\"mode\":\"normal\",\"style\":\"\",\"text\":\"bold italic\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\", \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":8,\"mode\":\"normal\",\"style\":\"\",\"text\":\"strikethrough\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\", and \",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":16,\"mode\":\"normal\",\"style\":\"\",\"text\":\"inline code\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\".\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"callout\",\"version\":1,\"calloutText\":\"<p dir=\\\"ltr\\\"><span style=\\\"white-space: pre-wrap;\\\">This is a callout</span></p>\",\"calloutEmoji\":\"💡\",\"backgroundColor\":\"green\"},{\"type\":\"markdown\",\"version\":1,\"markdown\":\"|ID|Name|\\n|---|---|\\n|1|Jane|\\n|2|John|\\n|3|Jade|\"},{\"children\":[],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}", "comment_id": "68a45b765f47170001b8c572", "feature_image": null, "featured": false, "status": "draft", "visibility": "public", "created_at": "2025-08-19T11:09:42.000Z", "updated_at": "2025-08-19T11:38:32.000Z", "published_at": null, "custom_excerpt": null, "codeinjection_head": null, "codeinjection_foot": null, "custom_template": null, "canonical_url": null, "tags": [], "tiers": [{"id": "68a3d14ea066a500081abf24", "name": "Free", "slug": "free", "active": true, "welcome_page_url": null, "visibility": "public", "trial_days": 0, "description": null, "type": "free", "currency": null, "monthly_price": null, "yearly_price": null, "created_at": "2025-08-19T01:20:14.000Z", "updated_at": "2025-08-19T01:20:14.000Z", "monthly_price_id": null, "yearly_price_id": null}, {"id": "68a3d14ea066a500081abf25", "name": "Test", "slug": "default-product", "active": true, "welcome_page_url": null, "visibility": "public", "trial_days": 0, "description": null, "type": "paid", "currency": "usd", "monthly_price": 500, "yearly_price": 5000, "created_at": "2025-08-19T01:20:14.000Z", "updated_at": "2025-08-19T07:28:28.000Z", "monthly_price_id": null, "yearly_price_id": null}], "authors": [{"id": "68a3d14ea066a500081abf15", "name": "<PERSON>", "slug": "peter", "email": "<EMAIL>", "profile_image": null, "cover_image": null, "bio": null, "website": null, "location": null, "facebook": null, "twitter": null, "threads": null, "bluesky": null, "mastodon": null, "tiktok": null, "youtube": null, "instagram": null, "linkedin": null, "accessibility": "{\"onboarding\":{\"completedSteps\":[],\"checklistState\":\"dismissed\"},\"whatsNew\":{\"lastSeenDate\":\"2025-08-04T08:10:56.000+00:00\"}}", "status": "active", "meta_title": null, "meta_description": null, "tour": null, "last_seen": "2025-08-19T10:39:39.000Z", "comment_notifications": true, "free_member_signup_notification": true, "paid_subscription_started_notification": true, "paid_subscription_canceled_notification": false, "mention_notifications": true, "recommendation_notifications": true, "milestone_notifications": true, "donation_notifications": true, "created_at": "2025-08-19T01:20:14.000Z", "updated_at": "2025-08-19T10:39:39.000Z", "roles": [{"id": "68a3d14ea066a500081abf1a", "name": "Owner", "description": "Blog Owner", "created_at": "2025-08-19T01:20:14.000Z", "updated_at": "2025-08-19T01:20:14.000Z"}], "url": "https://obsidian-plugin.ghost.io/author/peter/"}], "count": {"clicks": 0, "positive_feedback": 0, "negative_feedback": 0}, "primary_author": {"id": "68a3d14ea066a500081abf15", "name": "<PERSON>", "slug": "peter", "email": "<EMAIL>", "profile_image": null, "cover_image": null, "bio": null, "website": null, "location": null, "facebook": null, "twitter": null, "threads": null, "bluesky": null, "mastodon": null, "tiktok": null, "youtube": null, "instagram": null, "linkedin": null, "accessibility": "{\"onboarding\":{\"completedSteps\":[],\"checklistState\":\"dismissed\"},\"whatsNew\":{\"lastSeenDate\":\"2025-08-04T08:10:56.000+00:00\"}}", "status": "active", "meta_title": null, "meta_description": null, "tour": null, "last_seen": "2025-08-19T10:39:39.000Z", "comment_notifications": true, "free_member_signup_notification": true, "paid_subscription_started_notification": true, "paid_subscription_canceled_notification": false, "mention_notifications": true, "recommendation_notifications": true, "milestone_notifications": true, "donation_notifications": true, "created_at": "2025-08-19T01:20:14.000Z", "updated_at": "2025-08-19T10:39:39.000Z", "roles": [{"id": "68a3d14ea066a500081abf1a", "name": "Owner", "description": "Blog Owner", "created_at": "2025-08-19T01:20:14.000Z", "updated_at": "2025-08-19T01:20:14.000Z"}], "url": "https://obsidian-plugin.ghost.io/author/peter/"}, "primary_tag": null, "email_segment": "all", "url": "https://obsidian-plugin.ghost.io/p/9f728743-1063-448a-b19b-29ec9254b87d/", "excerpt": "Obsidian Ghost Sync - Formatting Test\n\nThis post contains all formatting elements we want to support.\n\n\nText Formatting\n\nHere we test bold text, italic text, bold italic, strikethrough, and inline code.\n\n💡This is a callout\n\n\n\n\nID\nName\n\n\n\n\n1\nJane\n\n\n2\nJohn\n\n\n3\nJade\n\n\n\n", "reading_time": 0, "og_image": null, "og_title": null, "og_description": null, "twitter_image": null, "twitter_title": null, "twitter_description": null, "meta_title": null, "meta_description": null, "email_subject": null, "frontmatter": null, "feature_image_alt": null, "feature_image_caption": null, "email_only": false, "email": null, "newsletter": null}