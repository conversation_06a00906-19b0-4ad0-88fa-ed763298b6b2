---
slug: sync-existing-test
title: Sync Existing Test
status: published
featured: false
visibility: public
tags:
  - test
  - sync
  - existing
---

# Sync Existing Test

This is a test post for syncing over an existing file from Ghost post browser.

## Test Content

This post tests the scenario where a local file already exists and we're syncing from <PERSON> to update it.

The sync process should handle this content properly even when the local file already has frontmatter and content.

## Key Points

- The post should sync successfully from Ghost
- The existing file should be updated with Ghost content
- No frontmatter cache errors should occur
- The content should be replaced with Ghost content

This is a test post for syncing existing files.

**End of existing test content.**
