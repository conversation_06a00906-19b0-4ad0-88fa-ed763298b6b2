import { describe, it, expect } from 'vitest';
import { ContentConverter } from '../../src/utils/content-converter';

describe('ContentConverter', () => {
  describe('createGhostPostData', () => {
    it('should generate proper Lexical content for all cases', async () => {
      const frontMatter = {
        title: 'Test Post',
        slug: 'test-post'
      };

      // Test with empty content
      const emptyContentResult = await ContentConverter.createGhostPostData(frontMatter, '');
      expect(emptyContentResult.lexical).toBeTruthy();
      expect(typeof emptyContentResult.lexical).toBe('string');

      // Test with whitespace-only content
      const whitespaceResult = await ContentConverter.createGhostPostData(frontMatter, '   \n  \t  ');
      expect(whitespaceResult.lexical).toBeTruthy();
      expect(typeof whitespaceResult.lexical).toBe('string');

      // Test with actual content
      const contentResult = await ContentConverter.createGhostPostData(frontMatter, 'Real content here');
      expect(contentResult.lexical).toBeTruthy();
      expect(typeof contentResult.lexical).toBe('string');

      // Parse and verify the lexical content contains the text
      const lexicalDoc = JSON.parse(contentResult.lexical);
      expect(lexicalDoc.root).toBeDefined();
      expect(lexicalDoc.root.type).toBe('root');
    });

    it('should handle markdown conversion properly', async () => {
      const frontMatter = {
        title: 'Test Post',
        slug: 'test-post'
      };

      const markdownContent = 'Some content that should convert';

      const result = await ContentConverter.createGhostPostData(frontMatter, markdownContent);

      // Should not throw and should contain actual content
      expect(result.lexical).toBeTruthy();
      expect(typeof result.lexical).toBe('string');

      // Parse and verify the lexical content
      const lexicalDoc = JSON.parse(result.lexical);
      expect(lexicalDoc.root).toBeDefined();
      expect(lexicalDoc.root.type).toBe('root');
    });

    it('should preserve existing feature image on updates', async () => {
      const frontMatter = {
        title: 'Test Post',
        slug: 'test-post'
      };

      const existingPost = {
        id: '1',
        feature_image: 'https://example.com/image.jpg',
        updated_at: '2024-01-01T10:00:00.000Z'
      };

      const result = await ContentConverter.createGhostPostData(
        frontMatter,
        'Content',
        { isUpdate: true, existingPost }
      );

      expect(result.feature_image).toBe('https://example.com/image.jpg');
    });

    it('should include sync timestamp when syncing TO Ghost', async () => {
      const frontMatter = {
        title: 'Test Post',
        slug: 'test-post',
        synced_at: '2024-01-01T09:00:00.000Z'
      };

      const result = await ContentConverter.createGhostPostData(frontMatter, 'Content');

      // The sync timestamp should be preserved in the post data for reference
      expect(result.title).toBe('Test Post');
      expect(result.slug).toBe('test-post');
    });
  });

  describe('convertGhostPostToArticle', () => {
    it('should NOT include internal sync timestamps in frontmatter', async () => {
      const ghostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T10:00:00.000Z',
        updated_at: '2024-01-01T10:00:00.000Z',
        html: '<p>Test content</p>',
        tags: [] as any[]
      };

      await expect(ContentConverter.convertGhostPostToArticle(ghostPost))
        .rejects.toThrow('Post "Test Post" has no lexical content. Only lexical content is supported.');
    });

    it('should handle posts with lexical content', async () => {
      const ghostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T10:00:00.000Z',
        updated_at: '2024-01-01T10:00:00.000Z',
        html: '<p>HTML content</p>',
        lexical: JSON.stringify({
          root: {
            type: 'root',
            children: [
              {
                type: 'markdown',
                version: 1,
                markdown: '# Markdown Content\n\nThis is markdown content.'
              }
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }
        }),
        tags: [] as any[]
      };

      const result = await ContentConverter.convertGhostPostToArticle(ghostPost);

      expect(result).toContain('# Markdown Content');
      expect(result).toContain('This is markdown content');
      expect(result).not.toContain('<p>HTML content</p>');
    });

    it('should throw error when lexical document is malformed', async () => {
      const ghostPost = {
        id: '1',
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T10:00:00.000Z',
        updated_at: '2024-01-01T10:00:00.000Z',
        html: '<p>HTML content</p>',
        lexical: JSON.stringify({
          root: {
            children: [
              {
                type: 'paragraph',
                children: [{ text: 'Some other content' }]
              }
            ]
          }
        }),
        tags: [] as any[]
      };

      await expect(ContentConverter.convertGhostPostToArticle(ghostPost))
        .rejects.toThrow('Failed to process lexical content for post "Test Post"');
    });

    it('should handle empty content gracefully', async () => {
      const ghostPost = {
        id: '1',
        title: 'Empty Post',
        slug: 'empty-post',
        status: 'draft',
        featured: false,
        created_at: '2024-01-01T10:00:00.000Z',
        updated_at: '2024-01-01T10:00:00.000Z',
        html: '',
        tags: [] as any[]
      };

      await expect(ContentConverter.convertGhostPostToArticle(ghostPost))
        .rejects.toThrow('Post "Empty Post" has no lexical content. Only lexical content is supported.');
    });
  });

  describe('parseMarkdown', () => {
    it('should parse frontmatter and content correctly using Obsidian API', async () => {
      const content = `---
title: Test Post
slug: test-post
synced_at: 2024-01-01T10:00:00.000Z
---

# Test Content

This is the content.`;

      // Mock Obsidian file and app
      const mockFile = { path: 'test.md' };
      const mockApp = {
        metadataCache: {
          getFileCache: vi.fn().mockReturnValue({
            frontmatter: {
              title: 'Test Post',
              slug: 'test-post',
              synced_at: '2024-01-01T10:00:00.000Z'
            }
          })
        }
      };

      const result = ContentConverter.parseMarkdown(content, mockFile, mockApp);

      expect(result.frontMatter.title).toBe('Test Post');
      expect(result.frontMatter.slug).toBe('test-post');
      expect(result.frontMatter.synced_at).toBe('2024-01-01T10:00:00.000Z');
      expect(result.markdownContent).toBe('# Test Content\n\nThis is the content.');
    });

    it('should throw error when no frontmatter found in metadata cache', async () => {
      const content = 'Just content without frontmatter';
      const mockFile = { path: 'test.md' };
      const mockApp = {
        metadataCache: {
          getFileCache: vi.fn().mockReturnValue({
            frontmatter: null
          })
        }
      };

      expect(() => ContentConverter.parseMarkdown(content, mockFile, mockApp)).toThrow('No frontmatter found in file metadata cache');
    });

    it('should throw error when file or app is missing', async () => {
      const content = 'Some content';

      expect(() => ContentConverter.parseMarkdown(content, null, null)).toThrow('Obsidian file and app instance are required for parsing');
    });
  });


});
