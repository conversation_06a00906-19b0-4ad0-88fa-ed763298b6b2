import { describe, it, expect } from 'vitest';
import { PropertyMapper, PROPERTY_MAPPINGS } from '../../src/utils/property-mapping';

describe('PropertyMapper', () => {
  describe('getByGhostProperty', () => {
    it('should find mapping by Ghost property name', () => {
      const mapping = PropertyMapper.getByGhostProperty('title');
      expect(mapping).toBeDefined();
      expect(mapping?.obsidianProperty).toBe('Title');
      expect(mapping?.obsidianAlternative).toBe('title');
    });

    it('should return undefined for non-existent property', () => {
      const mapping = PropertyMapper.getByGhostProperty('non-existent');
      expect(mapping).toBeUndefined();
    });
  });

  describe('getByObsidianProperty', () => {
    it('should find mapping by Title Case property name', () => {
      const mapping = PropertyMapper.getByObsidianProperty('Title');
      expect(mapping).toBeDefined();
      expect(mapping?.ghostProperty).toBe('title');
    });

    it('should find mapping by lowercase property name', () => {
      const mapping = PropertyMapper.getByObsidianProperty('title');
      expect(mapping).toBeDefined();
      expect(mapping?.ghostProperty).toBe('title');
    });

    it('should return undefined for non-existent property', () => {
      const mapping = PropertyMapper.getByObsidianProperty('non-existent');
      expect(mapping).toBeUndefined();
    });
  });

  describe('extractGhostValue', () => {
    it('should extract simple values', () => {
      const ghostPost = { title: 'Test Post', slug: 'test-post' };
      const titleMapping = PropertyMapper.getByGhostProperty('title')!;

      const value = PropertyMapper.extractGhostValue(ghostPost, titleMapping);
      expect(value).toBe('Test Post');
    });

    it('should use custom extraction for tags', () => {
      const ghostPost = {
        tags: [{ name: 'tag1' }, { name: 'tag2' }]
      };
      const tagsMapping = PropertyMapper.getByGhostProperty('tags')!;

      const value = PropertyMapper.extractGhostValue(ghostPost, tagsMapping);
      expect(value).toEqual(['tag1', 'tag2']);
    });

    it('should use custom extraction for primary_tag', () => {
      const ghostPost = {
        primary_tag: { name: 'main-tag' }
      };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const value = PropertyMapper.extractGhostValue(ghostPost, primaryTagMapping);
      expect(value).toBe('main-tag');
    });

    it('should handle null primary_tag in Ghost post', () => {
      const ghostPost: any = {
        primary_tag: null
      };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const value = PropertyMapper.extractGhostValue(ghostPost, primaryTagMapping);
      expect(value).toBeNull();
    });

    it('should handle undefined primary_tag in Ghost post', () => {
      const ghostPost = {};
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const value = PropertyMapper.extractGhostValue(ghostPost, primaryTagMapping);
      expect(value).toBeNull();
    });

    it('should trim whitespace from Ghost primary_tag', () => {
      const ghostPost = {
        primary_tag: { name: '  main-tag  ' }
      };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const value = PropertyMapper.extractGhostValue(ghostPost, primaryTagMapping);
      expect(value).toBe('main-tag');
    });

    it('should handle empty primary_tag name from Ghost', () => {
      const ghostPost = {
        primary_tag: { name: '' }
      };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const value = PropertyMapper.extractGhostValue(ghostPost, primaryTagMapping);
      expect(value).toBeNull();
    });

    it('should handle whitespace-only primary_tag name from Ghost', () => {
      const ghostPost = {
        primary_tag: { name: '   \t\n  ' }
      };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const value = PropertyMapper.extractGhostValue(ghostPost, primaryTagMapping);
      expect(value).toBeNull();
    });

    it('should use custom extraction for email_sent', () => {
      const ghostPostWithEmail = { email: { id: '123' } };
      const ghostPostWithoutEmail = { email: null as any };
      const emailMapping = PropertyMapper.getByGhostProperty('email_sent')!;

      const valueWithEmail = PropertyMapper.extractGhostValue(ghostPostWithEmail, emailMapping);
      const valueWithoutEmail = PropertyMapper.extractGhostValue(ghostPostWithoutEmail, emailMapping);

      expect(valueWithEmail).toBe('Yes');
      expect(valueWithoutEmail).toBe('No');
    });
  });

  describe('extractObsidianValue', () => {
    it('should extract Title Case values', () => {
      const frontmatter = { 'Title': 'Test Post', 'Slug': 'test-post' };
      const titleMapping = PropertyMapper.getByGhostProperty('title')!;

      const value = PropertyMapper.extractObsidianValue(frontmatter, titleMapping);
      expect(value).toBe('Test Post');
    });

    it('should extract lowercase values', () => {
      const frontmatter = { title: 'Test Post', slug: 'test-post' };
      const titleMapping = PropertyMapper.getByGhostProperty('title')!;

      const value = PropertyMapper.extractObsidianValue(frontmatter, titleMapping);
      expect(value).toBe('Test Post');
    });

    it('should prefer Title Case over lowercase', () => {
      const frontmatter = { 'Title': 'Title Case', title: 'lowercase' };
      const titleMapping = PropertyMapper.getByGhostProperty('title')!;

      const value = PropertyMapper.extractObsidianValue(frontmatter, titleMapping);
      expect(value).toBe('Title Case');
    });

    it('should handle boolean values correctly', () => {
      const frontmatter = { 'Featured': false };
      const featuredMapping = PropertyMapper.getByGhostProperty('featured')!;

      const value = PropertyMapper.extractObsidianValue(frontmatter, featuredMapping);
      expect(value).toBe(false);
    });

    it('should trim whitespace from Obsidian primary tag', () => {
      const frontmatter = { 'Primary Tag': '  tutorial  ' };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const value = PropertyMapper.extractObsidianValue(frontmatter, primaryTagMapping);
      expect(value).toBe('tutorial');
    });

    it('should handle empty Obsidian primary tag', () => {
      const frontmatter = { 'Primary Tag': '' };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const value = PropertyMapper.extractObsidianValue(frontmatter, primaryTagMapping);
      expect(value).toBeNull();
    });

    it('should handle whitespace-only Obsidian primary tag', () => {
      const frontmatter = { 'Primary Tag': '   \t\n  ' };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const value = PropertyMapper.extractObsidianValue(frontmatter, primaryTagMapping);
      expect(value).toBeNull();
    });

    it('should handle null Obsidian primary tag', () => {
      const frontmatter: any = { 'Primary Tag': null };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const value = PropertyMapper.extractObsidianValue(frontmatter, primaryTagMapping);
      expect(value).toBeNull();
    });

    it('should handle non-string Obsidian primary tag', () => {
      const frontmatter = { 'Primary Tag': 123 };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const value = PropertyMapper.extractObsidianValue(frontmatter, primaryTagMapping);
      expect(value).toBeNull();
    });

    it('should prefer Title Case over lowercase for primary tag', () => {
      const frontmatter = { 'Primary Tag': 'Title Case', primary_tag: 'lowercase' };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const value = PropertyMapper.extractObsidianValue(frontmatter, primaryTagMapping);
      expect(value).toBe('Title Case');
    });

    it('should use custom extraction for Featured Image', () => {
      const frontmatter = { 'Featured Image': 'https://example.com/image.jpg' };
      const featureImageMapping = PropertyMapper.getByGhostProperty('feature_image')!;

      const value = PropertyMapper.extractObsidianValue(frontmatter, featureImageMapping);
      expect(value).toBe('https://example.com/image.jpg');
    });

    it('should use custom extraction for Email Sent', () => {
      const frontmatterYes = { 'Email Sent': 'Yes' };
      const frontmatterNo = { 'Email Sent': 'No' };
      const frontmatterBoolean = { 'Email Sent': true };
      const emailMapping = PropertyMapper.getByGhostProperty('email_sent')!;

      const valueYes = PropertyMapper.extractObsidianValue(frontmatterYes, emailMapping);
      const valueNo = PropertyMapper.extractObsidianValue(frontmatterNo, emailMapping);
      const valueBoolean = PropertyMapper.extractObsidianValue(frontmatterBoolean, emailMapping);

      expect(valueYes).toBe('Yes');
      expect(valueNo).toBe('No');
      expect(valueBoolean).toBe('Yes');
    });
  });

  describe('compareValues', () => {
    it('should compare simple values correctly', () => {
      const ghostPost = { title: 'Test Post' };
      const frontmatter = { 'Title': 'Test Post' };
      const titleMapping = PropertyMapper.getByGhostProperty('title')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, titleMapping);
      expect(result).toBe('synced');
    });

    it('should detect differences', () => {
      const ghostPost = { title: 'Ghost Title' };
      const frontmatter = { 'Title': 'Different Title' };
      const titleMapping = PropertyMapper.getByGhostProperty('title')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, titleMapping);
      expect(result).toBe('different');
    });

    it('should use custom comparison for featured boolean', () => {
      const ghostPost = { featured: false };
      const frontmatter = { 'Featured': false };
      const featuredMapping = PropertyMapper.getByGhostProperty('featured')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, featuredMapping);
      expect(result).toBe('synced');
    });

    it('should use custom comparison for tags', () => {
      const ghostPost = { tags: [{ name: 'tag1' }, { name: 'tag2' }] };
      const frontmatter = { 'Tags': ['tag1', 'tag2'] };
      const tagsMapping = PropertyMapper.getByGhostProperty('tags')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, tagsMapping);
      expect(result).toBe('synced');
    });

    it('should detect tag differences', () => {
      const ghostPost = { tags: [{ name: 'tag1' }, { name: 'tag2' }] };
      const frontmatter = { 'Tags': ['tag1', 'tag3'] };
      const tagsMapping = PropertyMapper.getByGhostProperty('tags')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, tagsMapping);
      expect(result).toBe('different');
    });

    it('should handle tag order differences', () => {
      const ghostPost = { tags: [{ name: 'tag2' }, { name: 'tag1' }] };
      const frontmatter = { 'Tags': ['tag1', 'tag2'] };
      const tagsMapping = PropertyMapper.getByGhostProperty('tags')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, tagsMapping);
      expect(result).toBe('synced'); // Should be synced because order doesn't matter
    });

    it('should handle visibility property correctly', () => {
      const ghostPost = { visibility: 'public' };
      const frontmatter = { 'Visibility': 'public' };
      const visibilityMapping = PropertyMapper.getByGhostProperty('visibility')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, visibilityMapping);
      expect(result).toBe('synced');
    });

    it('should detect visibility differences', () => {
      const ghostPost = { visibility: 'members' };
      const frontmatter = { 'Visibility': 'public' };
      const visibilityMapping = PropertyMapper.getByGhostProperty('visibility')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, visibilityMapping);
      expect(result).toBe('different');
    });

    it('should handle missing visibility in frontmatter', () => {
      const ghostPost = { visibility: 'public' };
      const frontmatter = {}; // No visibility property
      const visibilityMapping = PropertyMapper.getByGhostProperty('visibility')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, visibilityMapping);
      expect(result).toBe('synced'); // Both default to 'public', should be synced
    });

    it('should handle null visibility in Ghost', () => {
      const ghostPost = { visibility: null as any };
      const frontmatter = {}; // No visibility property
      const visibilityMapping = PropertyMapper.getByGhostProperty('visibility')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, visibilityMapping);
      expect(result).toBe('synced'); // Both default to 'public', should be synced
    });

    it('should handle undefined visibility in Ghost', () => {
      const ghostPost = {}; // No visibility property
      const frontmatter = {}; // No visibility property
      const visibilityMapping = PropertyMapper.getByGhostProperty('visibility')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, visibilityMapping);
      expect(result).toBe('synced'); // Both default to 'public', should be synced
    });

    it('should handle primary_tag property correctly', () => {
      const ghostPost = { primary_tag: { name: 'featured' } };
      const frontmatter = { 'Primary Tag': 'featured' };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, primaryTagMapping);
      expect(result).toBe('synced');
    });

    it('should detect primary_tag differences', () => {
      const ghostPost = { primary_tag: { name: 'featured' } };
      const frontmatter = { 'Primary Tag': 'tutorial' };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, primaryTagMapping);
      expect(result).toBe('different');
    });

    it('should handle missing primary_tag in frontmatter', () => {
      const ghostPost = { primary_tag: { name: 'featured' } };
      const frontmatter = {}; // No primary_tag property
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, primaryTagMapping);
      expect(result).toBe('different');
    });

    it('should handle null primary_tag in Ghost', () => {
      const ghostPost = { primary_tag: null as any };
      const frontmatter = {}; // No primary_tag property
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, primaryTagMapping);
      expect(result).toBe('synced'); // Both are null/undefined, should be synced
    });

    it('should handle null primary_tag in frontmatter vs null in Ghost', () => {
      const ghostPost = { primary_tag: null as any };
      const frontmatter = { 'Primary Tag': null as any };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, primaryTagMapping);
      expect(result).toBe('synced'); // Both are null, should be synced
    });

    it('should handle empty string primary_tag', () => {
      const ghostPost = { primary_tag: { name: '' } };
      const frontmatter = { 'Primary Tag': '' };
      const primaryTagMapping = PropertyMapper.getByGhostProperty('primary_tag')!;

      const result = PropertyMapper.compareValues(ghostPost, frontmatter, primaryTagMapping);
      expect(result).toBe('synced'); // Both empty strings become null, should be synced
    });
  });

  describe('normalizeToObsidian', () => {
    it('should convert lowercase properties to Title Case', () => {
      const frontmatter = {
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        tags: ['tag1', 'tag2'],
        featured: false
      };

      const normalized = PropertyMapper.normalizeToObsidian(frontmatter);

      expect(normalized['Title']).toBe('Test Post');
      expect(normalized['Slug']).toBe('test-post');
      expect(normalized['Status']).toBe('draft');
      expect(normalized['Tags']).toEqual(['tag1', 'tag2']);
      expect(normalized['Featured']).toBe(false);

      // Lowercase versions should be removed
      expect(normalized.title).toBeUndefined();
      expect(normalized.slug).toBeUndefined();
      expect(normalized.status).toBeUndefined();
      expect(normalized.tags).toBeUndefined();
      expect(normalized.featured).toBeUndefined();
    });

    it('should preserve Title Case properties', () => {
      const frontmatter = {
        'Title': 'Test Post',
        'Slug': 'test-post',
        'Featured Image': 'https://example.com/image.jpg'
      };

      const normalized = PropertyMapper.normalizeToObsidian(frontmatter);

      expect(normalized['Title']).toBe('Test Post');
      expect(normalized['Slug']).toBe('test-post');
      // Featured Image is not displayed in UI, so it won't be in normalized output
      expect(normalized['Featured Image']).toBeUndefined();
    });

    it('should preserve non-mapped properties', () => {
      const frontmatter = {
        title: 'Test Post',
        customProperty: 'custom value',
        anotherProp: 123
      };

      const normalized = PropertyMapper.normalizeToObsidian(frontmatter);

      expect(normalized['Title']).toBe('Test Post');
      expect(normalized.customProperty).toBe('custom value');
      expect(normalized.anotherProp).toBe(123);
    });

    it('should exclude properties that should not be displayed in UI', () => {
      const ghostData = {
        title: 'Test Post',
        slug: 'test-post',
        status: 'draft',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T12:00:00.000Z',
        published_at: '2023-01-01T10:00:00.000Z'
      };

      const result = PropertyMapper.normalizeToObsidian(ghostData);

      // Should include displayable properties
      expect(result.Title).toBe('Test Post');
      expect(result.Slug).toBe('test-post');
      expect(result.Status).toBe('draft');

      // Should NOT include non-displayable properties
      expect(result['Created At']).toBeUndefined();
      expect(result['Updated At']).toBeUndefined();
      expect(result['Published At']).toBeUndefined();
      expect(result.created_at).toBeUndefined();
      expect(result.updated_at).toBeUndefined();
      expect(result.published_at).toBeUndefined();
    });
  });

  describe('normalizeToGhost', () => {
    it('should convert Title Case properties to lowercase', () => {
      const frontmatter = {
        'Title': 'Test Post',
        'Slug': 'test-post',
        'Status': 'draft',
        'Tags': ['tag1', 'tag2'],
        'Featured': false,
        'Featured Image': 'https://example.com/image.jpg'
      };

      const normalized = PropertyMapper.normalizeToGhost(frontmatter);

      expect(normalized.title).toBe('Test Post');
      expect(normalized.slug).toBe('test-post');
      expect(normalized.status).toBe('draft');
      expect(normalized.tags).toEqual(['tag1', 'tag2']);
      expect(normalized.featured).toBe(false);
      expect(normalized.feature_image).toBe('https://example.com/image.jpg');
    });

    it('should handle mixed case properties', () => {
      const frontmatter = {
        'Title': 'Test Post',
        slug: 'test-post', // lowercase
        'Status': 'draft',
        tags: ['tag1', 'tag2'] // lowercase
      };

      const normalized = PropertyMapper.normalizeToGhost(frontmatter);

      expect(normalized.title).toBe('Test Post');
      expect(normalized.slug).toBe('test-post');
      expect(normalized.status).toBe('draft');
      expect(normalized.tags).toEqual(['tag1', 'tag2']);
    });

    it('should include both mapped and unmapped properties', () => {
      const frontmatter = {
        'Title': 'Test Post',
        customProperty: 'custom value',
        anotherProp: 123
      };

      const normalized = PropertyMapper.normalizeToGhost(frontmatter);

      expect(normalized.title).toBe('Test Post');
      expect(normalized.customProperty).toBe('custom value');
      expect(normalized.anotherProp).toBe(123);
    });
  });

  describe('property mappings configuration', () => {
    it('should have all required properties', () => {
      const requiredProperties = [
        'title', 'slug', 'status', 'tags', 'primary_tag',
        'visibility', 'featured', 'feature_image', 'newsletter', 'email_sent'
      ];

      for (const prop of requiredProperties) {
        const mapping = PropertyMapper.getByGhostProperty(prop);
        expect(mapping).toBeDefined();
        expect(mapping?.includeInSync).toBe(true);

        // Feature image and primary tag are synced but not displayed in UI
        if (prop === 'feature_image' || prop === 'primary_tag') {
          expect(mapping?.displayInUI).toBe(false);
        } else {
          expect(mapping?.displayInUI).toBe(true);
        }
      }
    });

    it('should have proper display properties', () => {
      const displayProperties = PropertyMapper.getDisplayProperties();
      expect(displayProperties.length).toBeGreaterThan(0);

      // Check that key properties are included
      const displayPropertyNames = displayProperties.map(p => p.ghostProperty);
      expect(displayPropertyNames).toContain('title');
      expect(displayPropertyNames).toContain('slug');
      expect(displayPropertyNames).toContain('status');
      expect(displayPropertyNames).toContain('tags');
      expect(displayPropertyNames).toContain('featured');
    });

    it('should have proper sync properties', () => {
      const syncProperties = PropertyMapper.getSyncProperties();
      expect(syncProperties.length).toBeGreaterThan(0);

      // Check that key properties are included
      const syncPropertyNames = syncProperties.map(p => p.ghostProperty);
      expect(syncPropertyNames).toContain('title');
      expect(syncPropertyNames).toContain('slug');
      expect(syncPropertyNames).toContain('status');
      expect(syncPropertyNames).toContain('tags');
      expect(syncPropertyNames).toContain('featured');
    });
  });
});
