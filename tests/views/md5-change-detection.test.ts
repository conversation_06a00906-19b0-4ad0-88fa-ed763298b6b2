import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TFile } from 'obsidian';
import { ContentConverter } from '../../src/utils/content-converter';
import { PropertyMapper } from '../../src/utils/property-mapping';
import * as crypto from 'crypto';

// Mock the dependencies
vi.mock('../../src/utils/content-converter', () => ({
  ContentConverter: {
    parseMarkdown: vi.fn()
    // NOTE: updateChangedTimestamp has been removed - timestamps are now stored in SyncMetadataStorage
  }
}));

vi.mock('../../src/utils/property-mapping', () => ({
  PropertyMapper: {
    getSyncProperties: vi.fn(),
    extractObsidianValue: vi.fn()
  }
}));

describe('MD5 Change Detection Logic', () => {
  let mockApp: any;
  let mockFile: TFile;
  let contentHashes: Map<string, string>;
  let isUpdatingTimestamp: boolean;

  // Simulate the calculateSyncContentHash method
  function calculateSyncContentHash(content: string): string {
    // For testing, we'll create a simple hash based on the content
    // In the real implementation, this would use the parsed content and properties
    return crypto.createHash('md5').update(content).digest('hex');
  }

  // Simulate the handleFileModification method
  async function handleFileModification(app: any, file: TFile) {
    if (isUpdatingTimestamp) {
      return;
    }

    const currentContent = await app.vault.read(file);
    const parsed = ContentConverter.parseMarkdown(currentContent, file, app);
    const slug = parsed.frontMatter.slug || parsed.frontMatter.Slug;

    if (!slug) {
      return;
    }

    const currentHash = calculateSyncContentHash(currentContent);
    const previousHash = contentHashes.get(file.path);

    if (previousHash && currentHash !== previousHash) {
      // NOTE: updateChangedTimestamp has been removed - timestamps are now stored in SyncMetadataStorage
      // This test logic is no longer relevant since we don't modify frontmatter for timestamps
      contentHashes.set(file.path, currentHash);
    } else if (!previousHash) {
      contentHashes.set(file.path, currentHash);
    } else {
      contentHashes.set(file.path, currentHash);
    }
  }

  beforeEach(() => {
    vi.clearAllMocks();
    contentHashes = new Map();
    isUpdatingTimestamp = false;

    mockApp = {
      vault: {
        read: vi.fn(),
        modify: vi.fn()
      }
    };

    mockFile = {
      path: 'test-article.md',
      name: 'test-article.md',
      basename: 'test-article',
      extension: 'md'
    } as TFile;

    // Mock PropertyMapper.getSyncProperties to return some test mappings
    vi.mocked(PropertyMapper.getSyncProperties).mockReturnValue([
      { obsidianProperty: 'title', ghostProperty: 'title', includeInSync: true, displayInUI: true },
      { obsidianProperty: 'slug', ghostProperty: 'slug', includeInSync: true, displayInUI: true },
      { obsidianProperty: 'status', ghostProperty: 'status', includeInSync: true, displayInUI: true }
    ]);
  });

  it('should update timestamp when content changes', async () => {
    const originalContent = `---
title: Same Title
slug: test-post
status: draft
---

# Same Title

Original content here.`;

    const modifiedContent = `---
title: Same Title
slug: test-post
status: draft
---

# Same Title

Updated content here.`;

    const timestampedContent = `---
title: Same Title
slug: test-post
status: draft
changed_at: 2024-01-01T12:00:00.000Z
---

# Same Title

Updated content here.`;

    // First call - cache the original content
    mockApp.vault.read.mockResolvedValueOnce(originalContent);
    vi.mocked(ContentConverter.parseMarkdown).mockReturnValueOnce({
      frontMatter: { slug: 'test-post' },
      markdownContent: 'content'
    });

    await handleFileModification(mockApp, mockFile);

    // Second call - content changed
    mockApp.vault.read.mockResolvedValueOnce(modifiedContent);
    vi.mocked(ContentConverter.parseMarkdown).mockReturnValueOnce({
      frontMatter: { slug: 'test-post' },
      markdownContent: 'content'
    });

    await handleFileModification(mockApp, mockFile);

    // NOTE: updateChangedTimestamp has been removed - timestamps are now stored in SyncMetadataStorage
    // We no longer modify frontmatter for timestamps
    expect(mockApp.vault.modify).not.toHaveBeenCalled();
  });

  it('should NOT update timestamp when content is identical', async () => {
    const sameContent = `---
title: Same Title
slug: test-post
status: draft
---

# Same Title

Same content here.`;

    // First call - cache the content
    mockApp.vault.read.mockResolvedValueOnce(sameContent);
    vi.mocked(ContentConverter.parseMarkdown).mockReturnValueOnce({
      frontMatter: { slug: 'test-post' },
      markdownContent: 'content'
    });

    await handleFileModification(mockApp, mockFile);

    // Second call - identical content
    mockApp.vault.read.mockResolvedValueOnce(sameContent);
    vi.mocked(ContentConverter.parseMarkdown).mockReturnValueOnce({
      frontMatter: { slug: 'test-post' },
      markdownContent: 'content'
    });

    await handleFileModification(mockApp, mockFile);

    // NOTE: updateChangedTimestamp has been removed - timestamps are now stored in SyncMetadataStorage
    // We no longer modify frontmatter for timestamps
    expect(mockApp.vault.modify).not.toHaveBeenCalled();
  });


});
