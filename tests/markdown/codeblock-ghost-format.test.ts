/**
 * Test that validates our markdown parser generates proper Ghost codeblock format
 * This test specifically addresses the issue where fenced code blocks were not
 * being converted to Ghost's expected codeblock lexical nodes
 */

import { describe, it, expect } from 'vitest';
import { markdownToLexical } from '../../src/markdown';

describe('Ghost Codeblock Format Test', () => {
  it('should convert fenced code blocks to Ghost codeblock format', async () => {
    const markdown = `# Test

Here's some JavaScript:

\`\`\`javascript
function hello() {
  console.log("Hello world!");
  return "success";
}
\`\`\`

And some Python:

\`\`\`python
def greet(name):
    print(f"Hello, {name}!")
    return True
\`\`\`

End of test.`;

    const result = await markdownToLexical(markdown);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    
    const children = result.data!.root.children;
    
    // Find the JavaScript code block
    const jsCodeBlock = children.find((child: any) => 
      child.type === 'codeblock' && child.language === 'javascript'
    );
    
    expect(jsCodeBlock).toBeDefined();
    expect(jsCodeBlock).toEqual({
      type: 'codeblock',
      version: 1,
      code: 'function hello() {\n  console.log("Hello world!");\n  return "success";\n}',
      language: 'javascript',
      caption: ''
    });
    
    // Find the Python code block
    const pythonCodeBlock = children.find((child: any) => 
      child.type === 'codeblock' && child.language === 'python'
    );
    
    expect(pythonCodeBlock).toBeDefined();
    expect(pythonCodeBlock).toEqual({
      type: 'codeblock',
      version: 1,
      code: 'def greet(name):\n    print(f"Hello, {name}!")\n    return True',
      language: 'python',
      caption: ''
    });
  });

  it('should handle code blocks without language specification', async () => {
    const markdown = `\`\`\`
plain code block
no language specified
\`\`\``;

    const result = await markdownToLexical(markdown);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    
    const codeBlock = result.data!.root.children.find((child: any) => 
      child.type === 'codeblock'
    );
    
    expect(codeBlock).toBeDefined();
    expect(codeBlock).toEqual({
      type: 'codeblock',
      version: 1,
      code: 'plain code block\nno language specified',
      language: '',
      caption: ''
    });
  });

  it('should NOT generate old-style code nodes with children', async () => {
    const markdown = `\`\`\`typescript
interface User {
  name: string;
  age: number;
}
\`\`\``;

    const result = await markdownToLexical(markdown);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    
    const children = result.data!.root.children;
    
    // Should not have any nodes with type 'code' that have children
    const oldStyleCodeNodes = children.filter((child: any) => 
      child.type === 'code' && child.children
    );
    
    expect(oldStyleCodeNodes).toHaveLength(0);
    
    // Should have proper codeblock node
    const codeBlock = children.find((child: any) => child.type === 'codeblock');
    expect(codeBlock).toBeDefined();
    expect(codeBlock.type).toBe('codeblock');
    expect(codeBlock.code).toContain('interface User');
    expect(codeBlock.language).toBe('typescript');
    expect(codeBlock.caption).toBe('');
    expect(codeBlock.children).toBeUndefined(); // Should not have children property
  });
});
