/**
 * Test that validates our markdown parser generates proper Ghost callout format
 * This test specifically addresses the issue where Obsidian callouts were not
 * being converted to Ghost's expected callout lexical nodes
 */

import { describe, it, expect } from 'vitest';
import { markdownToLexical } from '../../src/markdown';

describe('Ghost Callout Format Test', () => {
  it('should convert Obsidian callouts to Ghost callout format', async () => {
    const markdown = `> [!info]
> This is an info callout`;

    const result = await markdownToLexical(markdown);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    
    const children = result.data!.root.children;
    
    // Find the callout node
    const callout = children.find((child: any) => child.type === 'callout');
    
    expect(callout).toBeDefined();
    expect(callout).toEqual({
      type: 'callout',
      version: 1,
      calloutText: '<p dir="ltr"><span style="white-space: pre-wrap;">This is an info callout</span></p>',
      calloutEmoji: 'ℹ️',
      backgroundColor: 'blue'
    });
  });

  it('should handle different callout types with correct emojis and colors', async () => {
    const testCases = [
      { type: 'note', emoji: '📝', color: 'grey' },
      { type: 'info', emoji: 'ℹ️', color: 'blue' },
      { type: 'tip', emoji: '💡', color: 'green' },
      { type: 'warning', emoji: '⚠️', color: 'yellow' },
      { type: 'danger', emoji: '❌', color: 'red' },
      { type: 'success', emoji: '✅', color: 'green' }
    ];

    for (const testCase of testCases) {
      const markdown = `> [!${testCase.type}]
> This is a ${testCase.type} callout`;

      const result = await markdownToLexical(markdown);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      
      const callout = result.data!.root.children.find((child: any) => 
        child.type === 'callout'
      );
      
      expect(callout).toBeDefined();
      expect(callout.type).toBe('callout');
      expect(callout.calloutEmoji).toBe(testCase.emoji);
      expect(callout.backgroundColor).toBe(testCase.color);
      expect(callout.calloutText).toContain(`This is a ${testCase.type} callout`);
    }
  });

  it('should handle multiline callouts', async () => {
    const markdown = `> [!warning]
> Line 1 of callout
> Line 2 of callout
> Line 3 of callout`;

    const result = await markdownToLexical(markdown);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    
    const callout = result.data!.root.children.find((child: any) => 
      child.type === 'callout'
    );
    
    expect(callout).toBeDefined();
    expect(callout.type).toBe('callout');
    expect(callout.calloutEmoji).toBe('⚠️');
    expect(callout.backgroundColor).toBe('yellow');
    expect(callout.calloutText).toContain('Line 1 of callout');
    expect(callout.calloutText).toContain('Line 2 of callout');
    expect(callout.calloutText).toContain('Line 3 of callout');
  });

  it('should NOT convert regular blockquotes to callouts', async () => {
    const markdown = `> This is a regular blockquote
> without callout syntax`;

    const result = await markdownToLexical(markdown);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    
    const children = result.data!.root.children;
    
    // Should not have any callout nodes
    const callouts = children.filter((child: any) => child.type === 'callout');
    expect(callouts).toHaveLength(0);
    
    // Should have a quote node instead
    const quote = children.find((child: any) => child.type === 'quote');
    expect(quote).toBeDefined();
    expect(quote.type).toBe('quote');
  });

  it('should NOT generate old-style quote nodes for callouts', async () => {
    const markdown = `> [!error]
> This is an error message`;

    const result = await markdownToLexical(markdown);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    
    const children = result.data!.root.children;
    
    // Should not have any quote nodes with callout markers
    const quotesWithCallouts = children.filter((child: any) => 
      child.type === 'quote' && 
      child.children && 
      child.children.some((grandchild: any) => 
        grandchild.text && grandchild.text.includes('[!')
      )
    );
    
    expect(quotesWithCallouts).toHaveLength(0);
    
    // Should have proper callout node
    const callout = children.find((child: any) => child.type === 'callout');
    expect(callout).toBeDefined();
    expect(callout.type).toBe('callout');
    expect(callout.calloutEmoji).toBe('🚨');
    expect(callout.backgroundColor).toBe('red');
  });
});
