/**
 * Ported formatting tests from the original lexical-parser
 * These tests ensure comprehensive text formatting support in the new Markdown class
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { Markdown } from '../../src/markdown';

describe('Ported Formatting Tests', () => {
  let parser: Markdown;

  beforeEach(() => {
    parser = new Markdown();
  });

  afterEach(() => {
    parser.destroy();
  });

  describe('Basic Text Formatting', () => {
    it('should handle bold text with **', async () => {
      const markdown = '**bold text**';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('extended-text');
      expect(textNode.text).toBe('bold text');
      expect(textNode.format & 1).toBe(1); // Bold flag
    });

    it('should handle bold text with __', async () => {
      const markdown = '__bold text__';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('extended-text');
      expect(textNode.text).toBe('bold text');
      expect(textNode.format & 1).toBe(1); // Bold flag
    });

    it('should handle italic text with *', async () => {
      const markdown = '*italic text*';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('extended-text');
      expect(textNode.text).toBe('italic text');
      expect(textNode.format & 2).toBe(2); // Italic flag
    });

    it('should handle italic text with _', async () => {
      const markdown = '_italic text_';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('extended-text');
      expect(textNode.text).toBe('italic text');
      expect(textNode.format & 2).toBe(2); // Italic flag
    });

    it('should handle strikethrough text', async () => {
      const markdown = '~~strikethrough text~~';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('extended-text');
      expect(textNode.text).toBe('strikethrough text');
      expect(textNode.format & 4).toBe(4); // Strikethrough flag
    });

    it('should handle inline code', async () => {
      const markdown = '`code text`';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('extended-text');
      expect(textNode.text).toBe('code text');
      expect(textNode.format & 16).toBe(16); // Code flag
    });
  });

  describe('Combined Formatting', () => {
    it('should handle bold and italic combined (***)', async () => {
      const markdown = '***bold and italic***';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('extended-text');
      expect(textNode.text).toBe('bold and italic');
      expect(textNode.format & 1).toBe(1); // Bold flag
      expect(textNode.format & 2).toBe(2); // Italic flag
    });

    it('should handle bold and italic combined (___)', async () => {
      const markdown = '___bold and italic___';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('extended-text');
      expect(textNode.text).toBe('bold and italic');
      expect(textNode.format & 1).toBe(1); // Bold flag
      expect(textNode.format & 2).toBe(2); // Italic flag
    });

    it('should handle nested formatting', async () => {
      const markdown = 'Normal **bold *and italic* text** normal';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      expect(paragraph.children.length).toBeGreaterThan(1);

      // Should have multiple text nodes with different formatting
      const hasPlainText = paragraph.children.some((node: any) => node.format === 0);
      const hasBoldText = paragraph.children.some((node: any) => (node.format & 1) === 1);
      const hasItalicText = paragraph.children.some((node: any) => (node.format & 2) === 2);

      expect(hasPlainText).toBe(true);
      expect(hasBoldText).toBe(true);
      expect(hasItalicText).toBe(true);
    });

    it('should handle code within other formatting', async () => {
      const markdown = '**Bold with `code` inside**';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      expect(paragraph.children.length).toBeGreaterThan(1);

      // Should have nodes with different formatting
      const hasBoldText = paragraph.children.some((node: any) => (node.format & 1) === 1);
      const hasCodeText = paragraph.children.some((node: any) => (node.format & 16) === 16);

      expect(hasBoldText).toBe(true);
      expect(hasCodeText).toBe(true);
    });
  });

  describe('Mixed Content Formatting', () => {
    it('should handle multiple formatting types in one paragraph', async () => {
      const markdown = 'This is **bold** and *italic* and `code` and ~~strikethrough~~ text.';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      expect(paragraph.children.length).toBeGreaterThan(1);

      // Should have multiple text nodes with different formatting
      const hasPlainText = paragraph.children.some((node: any) => node.format === 0);
      const hasBoldText = paragraph.children.some((node: any) => (node.format & 1) === 1);
      const hasItalicText = paragraph.children.some((node: any) => (node.format & 2) === 2);
      const hasStrikethroughText = paragraph.children.some((node: any) => (node.format & 4) === 4);
      const hasCodeText = paragraph.children.some((node: any) => (node.format & 16) === 16);

      expect(hasPlainText).toBe(true);
      expect(hasBoldText).toBe(true);
      expect(hasItalicText).toBe(true);
      expect(hasStrikethroughText).toBe(true);
      expect(hasCodeText).toBe(true);
    });

    it('should handle the exact formatting issue from user report', async () => {
      const markdown = 'This is a paragraph with **bold text** and an _italic using underscore_ text too and also *italic using star*. Some ~~strike-through~~ text here. Here\'s `inline code snippet`.';

      // Test markdown to lexical conversion
      const lexicalResult = await parser.markdownToLexical(markdown);
      expect(lexicalResult.success).toBe(true);

      // Check that the lexical document has the correct formatting flags
      const paragraph = lexicalResult.data?.root.children[0] as any;
      expect(paragraph.type).toBe('paragraph');

      // Find text nodes with different formatting
      const textNodes = paragraph.children;
      const boldNode = textNodes.find((node: any) => node.format & 1); // Bold flag
      const italicNodes = textNodes.filter((node: any) => node.format & 2); // Italic flag
      const strikethroughNode = textNodes.find((node: any) => node.format & 4); // Strikethrough flag
      const codeNode = textNodes.find((node: any) => node.format & 16); // Code flag

      expect(boldNode).toBeDefined();
      expect(boldNode.text).toBe('bold text');

      expect(italicNodes.length).toBe(2); // Should have both italic styles
      expect(italicNodes[0].text).toBe('italic using underscore');
      expect(italicNodes[1].text).toBe('italic using star');

      expect(strikethroughNode).toBeDefined();
      expect(strikethroughNode.text).toBe('strike-through');

      expect(codeNode).toBeDefined();
      expect(codeNode.text).toBe('inline code snippet');

      // The key insight: Lexical JSON preserves ALL formatting correctly!
      // Both italic nodes have format: 2 (italic flag), regardless of original syntax
      // Ghost will render this correctly because it uses the Lexical JSON directly
      console.log('✅ Lexical JSON correctly preserves all formatting with proper flags');
    });

    it('should handle formatting at word boundaries', async () => {
      const markdown = 'Start**bold**middle*italic*end';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      expect(paragraph.children.length).toBeGreaterThan(1);
    });

    it('should handle formatting with punctuation', async () => {
      const markdown = 'Hello, **world**! How are *you* today?';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      expect(paragraph.children.length).toBeGreaterThan(1);

      const hasBoldText = paragraph.children.some((node: any) => (node.format & 1) === 1);
      const hasItalicText = paragraph.children.some((node: any) => (node.format & 2) === 2);

      expect(hasBoldText).toBe(true);
      expect(hasItalicText).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty formatting', async () => {
      const markdown = '****';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      // Should handle gracefully without crashing
    });

    it('should handle unmatched formatting markers', async () => {
      const markdown = 'This has **unmatched bold';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      // Should handle gracefully, treating as literal text
    });

    it('should handle escaped formatting', async () => {
      const markdown = 'This has \\*escaped\\* asterisks';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.text).toContain('*escaped*');
    });

    it('should handle formatting across line breaks', async () => {
      const markdown = '**Bold text\nacross lines**';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      // Should handle gracefully
    });
  });

  describe('Ghost-specific Node Types', () => {
    it('should handle Ghost callout nodes', async () => {
      // Test the actual Ghost callout structure from our reference
      const ghostCalloutDoc = {
        root: {
          children: [
            {
              type: "callout",
              version: 1,
              calloutText: "<p dir=\"ltr\"><span style=\"white-space: pre-wrap;\">This is a callout</span></p>",
              calloutEmoji: "💡",
              backgroundColor: "green"
            }
          ],
          direction: "ltr",
          format: "",
          indent: 0,
          type: "root",
          version: 1
        }
      };

      const result = await parser.lexicalToMarkdown(ghostCalloutDoc);

      expect(result.success).toBe(true);
      expect(result.data).toBe('> [!tip]\n> This is a callout');
      console.log('✅ Ghost callout converted to Obsidian format');
    });

    it('should handle Ghost markdown nodes with tables', async () => {
      // Test the actual Ghost markdown structure from our reference
      const ghostMarkdownDoc = {
        root: {
          children: [
            {
              type: "markdown",
              version: 1,
              markdown: "|ID|Name|\n|---|---|\n|1|Jane|\n|2|John|\n|3|Jade|"
            }
          ],
          direction: "ltr",
          format: "",
          indent: 0,
          type: "root",
          version: 1
        }
      };

      const result = await parser.lexicalToMarkdown(ghostMarkdownDoc);

      expect(result.success).toBe(true);
      expect(result.data).toContain('|ID|Name|');
      expect(result.data).toContain('|1|Jane|');
      console.log('✅ Ghost markdown table preserved in code format');
    });

    it('should handle extended-text and extended-heading nodes', async () => {
      // Test the actual Ghost extended node structure from our reference
      const ghostExtendedDoc = {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: "normal",
                  style: "",
                  text: "Extended Heading",
                  type: "extended-text",
                  version: 1
                }
              ],
              direction: "ltr",
              format: "",
              indent: 0,
              type: "extended-heading",
              version: 1,
              tag: "h1"
            }
          ],
          direction: "ltr",
          format: "",
          indent: 0,
          type: "root",
          version: 1
        }
      };

      const result = await parser.lexicalToMarkdown(ghostExtendedDoc);

      expect(result.success).toBe(true);
      expect(result.data).toBe('# Extended Heading');
      console.log('✅ Ghost extended nodes converted to standard format');
    });

    it('should handle the complete Ghost reference document', async () => {
      // Load and test the actual Ghost reference JSON
      const fs = require('fs');
      const path = require('path');

      const referenceFile = path.join(process.cwd(), 'tests/fixtures/lexical-reference/obsidian-ghost-formatting-test.json');
      const referenceData = JSON.parse(fs.readFileSync(referenceFile, 'utf8'));
      const lexicalDoc = JSON.parse(referenceData.lexical);

      const result = await parser.lexicalToMarkdown(lexicalDoc);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.length).toBeGreaterThan(0);

      // Should contain the callout in Obsidian format
      expect(result.data).toContain('[!tip]');

      // Should contain the table data
      expect(result.data).toContain('Jane');

      console.log('✅ Complete Ghost reference document converted successfully');
      console.log('Generated markdown length:', result.data!.length);
    });
  });
});
