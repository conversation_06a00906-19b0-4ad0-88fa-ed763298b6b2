/**
 * Test that validates our markdown parser by testing roundtrip conversion
 * from source markdown through lexical back to markdown using the Ghost-generated lexical fixture
 */

import { describe, it, expect } from 'vitest';
import { lexicalToMarkdown } from '../../src/markdown';
import * as fs from 'fs';
import * as path from 'path';
import matter from 'gray-matter';

describe('Roundtrip Lexical Fixture Test', () => {
  it('should convert Ghost-generated lexical back to markdown that matches our source', async () => {
    // Read the source markdown fixture
    const sourceFixturePath = path.join(process.cwd(), 'tests/fixtures/sync-content-test.md');
    const sourceContent = fs.readFileSync(sourceFixturePath, 'utf8');
    
    // Parse the source to get just the content (without frontmatter)
    const parsed = matter(sourceContent);
    const expectedMarkdown = parsed.content.trim();
    
    // Read the lexical fixture that was generated by Ghost
    const lexicalFixturePath = path.join(process.cwd(), 'tests/fixtures/sync-content-test.lexical.json');
    const lexicalContent = fs.readFileSync(lexicalFixturePath, 'utf8');
    const lexicalDocument = JSON.parse(lexicalContent);
    
    // Convert the lexical document back to markdown
    const result = await lexicalToMarkdown(lexicalDocument);
    
    // The conversion should succeed
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    
    const actualMarkdown = result.data!.trim();
    
    // Log both for debugging
    console.log('Expected markdown:');
    console.log(expectedMarkdown);
    console.log('\nActual markdown:');
    console.log(actualMarkdown);
    
    // The converted markdown should match the original source
    // Note: Some formatting differences might be acceptable (e.g., spacing, list formatting)
    // but the content should be semantically equivalent
    expect(actualMarkdown).toBe(expectedMarkdown);
  });
  
  it('should preserve all content elements from the lexical fixture', async () => {
    // Read the lexical fixture
    const lexicalFixturePath = path.join(process.cwd(), 'tests/fixtures/sync-content-test.lexical.json');
    const lexicalContent = fs.readFileSync(lexicalFixturePath, 'utf8');
    const lexicalDocument = JSON.parse(lexicalContent);
    
    // Convert to markdown
    const result = await lexicalToMarkdown(lexicalDocument);
    expect(result.success).toBe(true);
    
    const markdown = result.data!;
    
    // Check that all major content elements are preserved
    expect(markdown).toContain('# Sync Content Test');
    expect(markdown).toContain('## Test Content');
    expect(markdown).toContain('## Key Points');
    expect(markdown).toContain('frontmatter cache error');
    expect(markdown).toContain('function testSync()');
    expect(markdown).toContain('console.log("Testing sync functionality")');
    expect(markdown).toContain('- The post should sync successfully');
    expect(markdown).toContain('- No frontmatter cache errors');
    expect(markdown).toContain('- The content should be preserved');
    expect(markdown).toContain('- The file should be created');
    expect(markdown).toContain('> [!info]');
    expect(markdown).toContain('This is an info callout');
    expect(markdown).toContain('**End of test content.**');
  });
});
