import { describe, it, expect } from 'vitest';
import { markdownToLexical, lexicalToMarkdown } from '../../src/markdown/index';

describe('Horizontal Rule Support', () => {
  it('should convert markdown horizontal rules to lexical nodes', async () => {
    const markdown = 'Before rule\n\n---\n\nAfter rule';

    const result = await markdownToLexical(markdown);

    expect(result.success).toBe(true);
    expect(result.data?.root.children).toHaveLength(3); // paragraph, hr, paragraph

    // Check that we have a horizontal rule node
    const hrNode = result.data?.root.children[1];
    expect(hrNode.type).toBe('horizontalrule');
  });

  it('should convert lexical horizontal rule nodes back to markdown', async () => {
    const lexicalDoc = {
      root: {
        type: 'root',
        children: [
          {
            type: 'paragraph',
            children: [
              {
                type: 'text',
                text: 'Before rule',
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                version: 1
              }
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          },
          {
            type: 'horizontalrule',
            version: 1
          },
          {
            type: 'paragraph',
            children: [
              {
                type: 'text',
                text: 'After rule',
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                version: 1
              }
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        version: 1
      }
    };

    const result = await lexicalToMarkdown(lexicalDoc);

    expect(result.success).toBe(true);
    expect(result.data).toContain('---');
    expect(result.data).toContain('Before rule');
    expect(result.data).toContain('After rule');
  });

  it('should handle different horizontal rule syntaxes', async () => {
    const testCases = ['---', '***', '___'];

    for (const hrSyntax of testCases) {
      const markdown = `Before\n\n${hrSyntax}\n\nAfter`;
      const result = await markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const hrNode = result.data?.root.children[1];
      expect(hrNode.type).toBe('horizontalrule');
    }
  });

  it('should round-trip horizontal rules correctly', async () => {
    const originalMarkdown = 'Before rule\n\n---\n\nAfter rule';

    // Convert to lexical
    const lexicalResult = await markdownToLexical(originalMarkdown);
    expect(lexicalResult.success).toBe(true);

    // Convert back to markdown
    const markdownResult = await lexicalToMarkdown(lexicalResult.data!);
    expect(markdownResult.success).toBe(true);

    // Should contain the horizontal rule
    expect(markdownResult.data).toContain('---');
    expect(markdownResult.data).toContain('Before rule');
    expect(markdownResult.data).toContain('After rule');
  });
});
