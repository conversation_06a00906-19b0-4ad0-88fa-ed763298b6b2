import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ObsidianAppAdapter } from '../../src/services/obsidian-app-adapter';
import { App, TFile } from 'obsidian';

describe('ObsidianAppAdapter', () => {
  let adapter: ObsidianAppAdapter;
  let mockApp: App;
  let mockFile: TFile;
  let mockRead: any;
  let mockGetFileCache: any;

  beforeEach(() => {
    mockRead = vi.fn();
    mockGetFileCache = vi.fn();

    mockApp = {
      vault: {
        read: mockRead
      },
      metadataCache: {
        getFileCache: mockGetFileCache
      }
    } as any;

    // Create a mock TFile object instead of using constructor
    mockFile = {
      path: 'test-article.md',
      name: 'test-article.md',
      basename: 'test-article',
      extension: 'md'
    } as TFile;
    adapter = new ObsidianAppAdapter(mockApp);
  });

  describe('readFile', () => {
    it('should read file content', async () => {
      const content = 'Test file content';
      mockRead.mockResolvedValue(content);

      const result = await adapter.readFile(mockFile);

      expect(result).toBe(content);
      expect(mockRead).toHaveBeenCalledWith(mockFile);
    });

    it('should handle read errors', async () => {
      const error = new Error('File not found');
      mockRead.mockRejectedValue(error);

      await expect(adapter.readFile(mockFile)).rejects.toThrow('File not found');
    });
  });

  describe('getFileMetadata', () => {
    it('should return metadata when cache exists', () => {
      const mockCache = {
        frontmatter: {
          title: 'Test Post',
          slug: 'test-post'
        }
      };
      mockGetFileCache.mockReturnValue(mockCache);

      const result = adapter.getFileMetadata(mockFile);

      expect(result).toEqual({
        frontmatter: mockCache.frontmatter,
        content: ''
      });
      expect(mockGetFileCache).toHaveBeenCalledWith(mockFile);
    });

    it('should return null when no cache exists', () => {
      mockGetFileCache.mockReturnValue(null);

      const result = adapter.getFileMetadata(mockFile);

      expect(result).toEqual({
        frontmatter: undefined,
        content: ''
      });
    });

    it('should handle cache errors gracefully', () => {
      mockGetFileCache.mockImplementation(() => {
        throw new Error('Cache error');
      });

      const result = adapter.getFileMetadata(mockFile);

      expect(result).toBeNull();
    });
  });

  describe('getFileMetadataWithRetry', () => {
    it('should return metadata on first try when available', async () => {
      const mockCache = {
        frontmatter: {
          title: 'Test Post',
          slug: 'test-post'
        }
      };
      mockGetFileCache.mockReturnValue(mockCache);

      const result = await adapter.getFileMetadataWithRetry(mockFile);

      expect(result).toEqual({
        frontmatter: mockCache.frontmatter,
        content: ''
      });
      expect(mockGetFileCache).toHaveBeenCalledTimes(1);
    });

    it('should retry when frontmatter is initially undefined', async () => {
      const mockCache = {
        frontmatter: {
          title: 'Test Post',
          slug: 'test-post'
        }
      };

      // First call returns no frontmatter, second call returns frontmatter
      mockGetFileCache
        .mockReturnValueOnce({ frontmatter: undefined })
        .mockReturnValueOnce(mockCache);

      const result = await adapter.getFileMetadataWithRetry(mockFile, 2, 10);

      expect(result).toEqual({
        frontmatter: mockCache.frontmatter,
        content: ''
      });
      expect(mockGetFileCache).toHaveBeenCalledTimes(2);
    });

    it('should return final result after max retries', async () => {
      // Always return no frontmatter
      mockGetFileCache.mockReturnValue({ frontmatter: undefined });

      const result = await adapter.getFileMetadataWithRetry(mockFile, 2, 10);

      expect(result).toEqual({
        frontmatter: undefined,
        content: ''
      });
      expect(mockGetFileCache).toHaveBeenCalledTimes(3); // 2 retries + 1 final call
    });

    it('should use default retry parameters', async () => {
      const mockCache = {
        frontmatter: {
          title: 'Test Post',
          slug: 'test-post'
        }
      };
      mockGetFileCache.mockReturnValue(mockCache);

      const result = await adapter.getFileMetadataWithRetry(mockFile);

      expect(result).toEqual({
        frontmatter: mockCache.frontmatter,
        content: ''
      });
    });
  });
});
