import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SmartSyncService } from '../../src/services/smart-sync-service';
import { SyncDecision } from '../../src/types';
import type { TFile } from 'obsidian';
import type { LocalPost } from '../../src/types';
import { SyncMetadataStorage } from '../../src/services/sync-metadata-storage';

describe('SmartSyncService', () => {
  let service: SmartSyncService;
  let mockGhostAPI: any;
  let mockReadFile: any;
  let mockWriteFile: any;
  let mockParseMarkdown: any;
  let syncMetadata: SyncMetadataStorage;
  let mockPlugin: any;
  let mockFile: TFile;

  beforeEach(() => {
    mockGhostAPI = {
      getPostBySlug: vi.fn(),
      getPostById: vi.fn(),
      createPost: vi.fn(),
      updatePost: vi.fn()
    };

    mockReadFile = vi.fn();
    mockWriteFile = vi.fn();
    mockParseMarkdown = vi.fn();

    // Create a real SyncMetadataStorage with a mock plugin
    mockPlugin = {
      loadData: vi.fn().mockResolvedValue({}),
      saveData: vi.fn().mockResolvedValue(undefined)
    };
    syncMetadata = new SyncMetadataStorage(mockPlugin);

    service = new SmartSyncService({
      ghostAPI: mockGhostAPI,
      readFile: mockReadFile,
      writeFile: mockWriteFile,
      parseMarkdown: mockParseMarkdown,
      syncMetadata: syncMetadata
    });

    mockFile = {
      path: 'articles/test-post.md',
      stat: {
        mtime: Date.now(),
        ctime: Date.now(),
        size: 1000
      }
    } as TFile;
  });

  describe('parseLocalPost', () => {
    it('should parse local post and extract basic information', async () => {
      const fileContent = `---
title: Test Post
slug: test-post
---

Test content here`;

      const parsedContent = {
        frontMatter: {
          title: 'Test Post',
          slug: 'test-post'
        },
        content: 'Test content here'
      };

      mockReadFile.mockResolvedValue(fileContent);
      mockParseMarkdown.mockReturnValue(parsedContent);

      const result = await service.parseLocalPost(mockFile);

      expect(result.frontMatter).toEqual(parsedContent.frontMatter);
      expect(result.content).toEqual(parsedContent.content);
      expect(result.filePath).toEqual(mockFile.path);
      expect(result.fileModifiedAt).toEqual(mockFile.stat!.mtime);
    });
  });

  describe('analyzeSyncNeeded', () => {
    it('should sync to Ghost when post does not exist', async () => {
      const localPost: LocalPost = {
        frontMatter: { title: 'Test Post', slug: 'test-post' },
        content: 'Test content',
        syncedAt: undefined,
        fileModifiedAt: Date.now(),
        filePath: 'test.md'
      };

      const analysis = await service.analyzeSyncNeeded(localPost);

      expect(analysis.decision).toBe(SyncDecision.SYNC_TO_GHOST);
      expect(analysis.reason).toBe("Post doesn't exist in Ghost yet");
    });
  });

  describe('syncToGhost', () => {
    it('should throw error when post has no slug', async () => {
      const localPost: LocalPost = {
        frontMatter: { title: 'Test Post' }, // No slug
        content: 'Test content',
        fileModifiedAt: Date.now(),
        filePath: 'test.md'
      };

      await expect(service.syncToGhost(localPost, mockFile)).rejects.toThrow('Post must have a slug to sync to Ghost');
    });
  });
});
