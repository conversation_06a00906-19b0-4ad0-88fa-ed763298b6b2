import { afterEach } from 'vitest';
import { captureScreenshotOnFailure, getSharedTestContext } from './shared-context';

/**
 * Global test failure handler that captures screenshots when tests fail
 * This should be imported in test files that need screenshot capture on failure
 */

let isFailureHandlerSetup = false;

export function setupTestFailureHandler() {
  if (isFailureHandlerSetup) {
    return;
  }
  
  isFailureHandlerSetup = true;
  
  afterEach(async (testContext) => {
    // Check if the test failed
    const testResult = (testContext as any)?.meta?.result;
    if (testResult?.state === 'fail' || testResult?.errors?.length > 0) {
      try {
        const context = await getSharedTestContext();
        const testName = (testContext as any)?.meta?.name?.replace(/[^a-zA-Z0-9]/g, '-') || 'unknown-test';
        await captureScreenshotOnFailure(context, `test-failure-${testName}`);
      } catch (error) {
        console.warn(`⚠️ Failed to capture screenshot on test failure: ${error.message}`);
      }
    }
  });
}

/**
 * Manually capture a screenshot with a custom name
 */
export async function captureDebugScreenshot(name: string) {
  try {
    const context = await getSharedTestContext();
    await captureScreenshotOnFailure(context, `debug-${name}`);
  } catch (error) {
    console.warn(`⚠️ Failed to capture debug screenshot: ${error.message}`);
  }
}
