import { createGhostAPIClient, type Ghost<PERSON><PERSON>lient } from './ghost-cleanup';
import { SharedTestContext, executeCommand, expectNotice } from './shared-context';
import { openGhostTab, clickSyncButton, waitForSyncToComplete } from './ghost-tab-helpers';
import * as fs from 'fs';
import * as path from 'path';
import matter from 'gray-matter';

/**
 * Test post manager that handles Ghost API operations and post fixtures
 */
export class TestPostManager {
  private ghostAPI: GhostAPIClient | null;
  private managedPostIds: Set<string> = new Set();

  constructor() {
    this.ghostAPI = createGhostAPIClient();
  }

  /**
   * Check if Ghost API is available
   */
  isAvailable(): boolean {
    return this.ghostAPI !== null;
  }

  /**
   * Get the Ghost API client (throws if not available)
   */
  getAPI(): GhostAPIClient {
    if (!this.ghostAPI) {
      throw new Error('Ghost API not available - check credentials');
    }
    return this.ghostAPI;
  }

  /**
   * Copy a test fixture to the vault and return the file path
   */
  async setupTestPost(context: SharedTestContext, fixtureName: string, targetFileName?: string): Promise<string> {
    const fixtureContent = fs.readFileSync(
      path.resolve(`tests/fixtures/${fixtureName}`),
      'utf8'
    );

    const fileName = targetFileName || fixtureName;
    const testFilePath = path.join(context.vaultPath, 'articles', fileName);
    await fs.promises.writeFile(testFilePath, fixtureContent);

    // Wait for file to be recognized
    await context.page.waitForTimeout(1000);

    return testFilePath;
  }

  /**
   * Open a test post in Obsidian
   */
  async openTestPost(context: SharedTestContext, fileName: string): Promise<void> {
    const baseName = path.basename(fileName, '.md');
    await executeCommand(context, 'Quick switcher: Open quick switcher');
    await context.page.keyboard.type(baseName);
    await context.page.keyboard.press('Enter');
    await context.page.waitForTimeout(500);
  }

  /**
   * Get a post by slug and track it for cleanup
   */
  async getPostBySlug(slug: string): Promise<any | null> {
    if (!this.ghostAPI) return null;

    const post = await this.ghostAPI.getPostBySlug(slug);
    if (post) {
      this.managedPostIds.add(post.id);
    }
    return post;
  }

  /**
   * Update a post in Ghost and track it for cleanup
   */
  async updatePost(postId: string, updates: any): Promise<any> {
    if (!this.ghostAPI) {
      throw new Error('Ghost API not available');
    }

    this.managedPostIds.add(postId);
    return await this.ghostAPI.updatePost(postId, updates);
  }

  /**
   * Add a paragraph to a post's lexical content
   */
  async addParagraphToPost(postId: string, text: string): Promise<any> {
    if (!this.ghostAPI) {
      throw new Error('Ghost API not available');
    }

    // Get the current post
    const posts = await this.ghostAPI.getPosts({ filter: `id:${postId}`, limit: 1 });
    if (posts.length === 0) {
      throw new Error(`Post with ID ${postId} not found`);
    }

    const post = posts[0];
    const updatedContent = post.lexical ? JSON.parse(post.lexical) : null;

    if (updatedContent && updatedContent.root && updatedContent.root.children) {
      // Add a new paragraph at the end
      updatedContent.root.children.push({
        type: 'paragraph',
        children: [{
          type: 'text',
          text: text,
          format: 0,
          style: '',
          mode: 'normal',
          detail: 0
        }],
        direction: 'ltr',
        format: '',
        indent: 0,
        version: 1
      });

      // Update the post with new content and excerpt to ensure Ghost detects the change
      return await this.updatePost(postId, {
        lexical: JSON.stringify(updatedContent),
        excerpt: 'Updated via API test - ' + new Date().toISOString(),
        updated_at: post.updated_at
      });
    } else {
      throw new Error('Could not parse post content for update');
    }
  }

  /**
   * Create a Ghost post from a fixture file using gray-matter for frontmatter parsing
   * This simplifies test setup by handling frontmatter parsing and lexical conversion
   */
  async createPostFromFixture(fixtureName: string): Promise<any> {
    if (!this.ghostAPI) {
      throw new Error('Ghost API not available');
    }

    const fs = await import('fs');
    const path = await import('path');

    // Read fixture content
    const fixtureContent = fs.readFileSync(
      path.resolve(`tests/fixtures/${fixtureName}`),
      'utf8'
    );

    // Parse frontmatter and content using gray-matter
    const parsed = matter(fixtureContent);
    const frontMatter = parsed.data;
    const markdownContent = parsed.content;

    // Validate that we have the required frontmatter
    if (!frontMatter.title) {
      throw new Error(`Fixture ${fixtureName} must have a title in frontmatter`);
    }

    // Use ContentConverter to create Ghost post data
    const { ContentConverter } = await import('../../src/utils/content-converter');
    const postData = await ContentConverter.createGhostPostData(frontMatter, markdownContent);

    // Create the post in Ghost
    const createdPost = await this.ghostAPI.createPost(postData);

    // Track for cleanup
    this.managedPostIds.add(createdPost.id);

    return createdPost;
  }

  /**
   * Create multiple Ghost posts from fixture files
   */
  async createPostsFromFixtures(fixtureNames: string[]): Promise<any[]> {
    const posts = [];
    for (const fixtureName of fixtureNames) {
      const post = await this.createPostFromFixture(fixtureName);
      posts.push(post);
    }
    return posts;
  }

  /**
   * Track a post for cleanup (for manually created posts)
   */
  trackPostForCleanup(postId: string): void {
    this.managedPostIds.add(postId);
  }

  /**
   * Clean up all managed posts
   */
  async cleanup(): Promise<void> {
    if (!this.ghostAPI || this.managedPostIds.size === 0) {
      return;
    }

    console.log(`🧹 Cleaning up ${this.managedPostIds.size} managed test posts...`);

    for (const postId of this.managedPostIds) {
      try {
        await this.ghostAPI.deletePost(postId);
        console.log(`✅ Cleaned up test post: ${postId}`);
      } catch (error) {
        console.log(`⚠️ Could not clean up test post ${postId}:`, error.message);
      }
    }

    this.managedPostIds.clear();
  }
}

/**
 * Simple sync helper that just clicks the sync button and waits for completion
 */
export async function syncPost(context: SharedTestContext): Promise<void> {
  await openGhostTab(context);
  await clickSyncButton(context.page);
  await waitForSyncToComplete(context.page);
  await expectNotice(context, "Synced");
}

/**
 * Setup a test post from fixture and open it in Obsidian
 */
export async function setupAndOpenTestPost(
  context: SharedTestContext,
  fixtureName: string,
  targetFileName?: string
): Promise<{ filePath: string; postManager: TestPostManager }> {
  const postManager = new TestPostManager();

  if (!postManager.isAvailable()) {
    throw new Error('Ghost API not available - check credentials');
  }

  const filePath = await postManager.setupTestPost(context, fixtureName, targetFileName);
  await postManager.openTestPost(context, targetFileName || fixtureName);

  return { filePath, postManager };
}
