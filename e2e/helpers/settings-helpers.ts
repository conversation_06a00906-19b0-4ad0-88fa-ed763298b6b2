import type { Page } from 'playwright';
import { executeCommand, type SharedTestContext } from './shared-context';

/**
 * Settings helpers for e2e tests
 */

/**
 * Wait for settings modal to be fully loaded
 */
async function waitForSettingsModal(page: Page): Promise<void> {
  await page.waitForSelector('.modal-container', { timeout: 10000 });

  const possibleNavSelectors = [
    '.vertical-tab-nav',
    '.setting-nav',
    '.nav-buttons-container',
    '.modal-setting-nav',
    '.setting-tab-nav',
    '.settings-nav'
  ];

  for (const selector of possibleNavSelectors) {
    try {
      await page.waitForSelector(selector, { timeout: 1000 });
      break;
    } catch (error) {
      continue;
    }
  }

  await page.waitForTimeout(1000);
}

/**
 * Open Ghost Sync plugin settings
 */
export async function openGhostSyncSettings(context: SharedTestContext): Promise<void> {
  await executeCommand(context, 'Open Settings');
  await waitForSettingsModal(context.page);

  const ghostSyncSelectors = [
    '.vertical-tab-nav-item:has-text("Ghost Sync")',
    '.vertical-tab-nav .vertical-tab-nav-item[data-tab="ghost-sync"]',
    '.vertical-tab-nav-item:text("Ghost Sync")',
    '.vertical-tab-nav :text("Ghost Sync")'
  ];

  for (const selector of ghostSyncSelectors) {
    try {
      const element = context.page.locator(selector);
      const count = await element.count();

      if (count > 0 && await element.first().isVisible()) {
        await element.first().click();
        await context.page.waitForSelector('text=Ghost Sync Settings', { timeout: 5000 });
        return;
      }
    } catch (error) {
      continue;
    }
  }

  throw new Error('Could not find Ghost Sync in settings modal');
}

/**
 * Close settings modal
 */
export async function closeSettings(page: Page): Promise<void> {
  const closeButton = page.locator('.modal-close-button, .modal-close, [aria-label="Close"]');
  if (await closeButton.count() > 0) {
    await closeButton.first().click();
  } else {
    await page.keyboard.press('Escape');
  }

  await page.waitForSelector('.modal-container', { state: 'detached', timeout: 3000 });
}

/**
 * Fill a setting input field
 */
export async function fillSettingInput(page: Page, placeholder: string, value: string): Promise<void> {
  const input = page.locator(`input[placeholder="${placeholder}"]`);
  await input.waitFor({ timeout: 5000 });
  await input.clear();
  await input.fill(value);
  await page.waitForTimeout(100);
}
