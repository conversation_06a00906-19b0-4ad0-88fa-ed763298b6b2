import type { Page } from 'playwright';

/**
 * Enhanced modal helpers for e2e tests
 * Provides unified, data-attribute-based modal interactions
 */

export interface ModalInfo {
  found: boolean;
  type: string;
  element?: any;
  modalType?: string;
}

/**
 * Wait for any modal to appear with enhanced detection
 */
export async function waitForModal(page: Page, timeout: number = 5000): Promise<ModalInfo> {
  try {
    const modalInfo = await page.waitForFunction(
      () => {
        // Check for standard Obsidian modals
        const modalContainer = document.querySelector('.modal-container');
        if (modalContainer) {
          const modalType = modalContainer.getAttribute('data-modal-type') || 'unknown';
          return { found: true, type: 'modal-container', modalType };
        }

        // Check for SuggestModal (used by PostSelectionModal)
        const suggester = document.querySelector('.suggester-container');
        if (suggester) {
          const modalType = suggester.getAttribute('data-modal-type') || 'unknown';
          return { found: true, type: 'suggester', modalType };
        }

        // Check for custom modals with modal-backdrop (Svelte components)
        const modalBackdrop = document.querySelector('.modal-backdrop');
        if (modalBackdrop) {
          const modalType = modalBackdrop.getAttribute('data-modal-type') || 'unknown';
          return { found: true, type: 'custom-modal', modalType };
        }

        return { found: false, type: 'none' };
      },
      {},
      { timeout }
    );

    return await modalInfo.jsonValue();
  } catch (error) {
    console.log(`No modal appeared within ${timeout}ms`);
    return { found: false, type: 'none' };
  }
}

/**
 * Wait for a specific modal type to appear
 */
export async function waitForModalType(page: Page, expectedType: string, timeout: number = 5000): Promise<ModalInfo> {
  try {
    const modalInfo = await page.waitForFunction(
      (expectedModalType) => {
        // Check all modal types for the expected data-modal-type
        const selectors = [
          '.modal-container',
          '.suggester-container',
          '.modal-backdrop'
        ];

        for (const selector of selectors) {
          const element = document.querySelector(selector);
          if (element) {
            const modalType = element.getAttribute('data-modal-type');
            if (modalType === expectedModalType) {
              return {
                found: true,
                type: selector.replace('.', '').replace('-container', '').replace('-backdrop', ''),
                modalType
              };
            }
          }
        }

        return { found: false, type: 'none' };
      },
      expectedType,
      { timeout }
    );

    return await modalInfo.jsonValue();
  } catch (error) {
    console.log(`Modal type "${expectedType}" did not appear within ${timeout}ms`);
    return { found: false, type: 'none' };
  }
}

/**
 * Fill input in modal using data attributes
 */
export async function fillModalInput(page: Page, inputName: string, value: string): Promise<void> {
  const selector = `[data-input="${inputName}"]`;
  await page.waitForSelector(selector);
  await page.fill(selector, value);
}

/**
 * Click button in modal using data attributes
 */
export async function clickModalAction(page: Page, action: string): Promise<void> {
  const selector = `[data-action="${action}"]`;
  await page.waitForSelector(selector);
  await page.click(selector);
}

/**
 * Select a post from post browser modal
 */
export async function selectPost(page: Page, postSlug: string): Promise<void> {
  const selector = `[data-post-slug="${postSlug}"]`;
  await page.waitForSelector(selector);
  await page.click(selector);
}

/**
 * Close any modal using multiple strategies
 */
export async function closeModal(page: Page): Promise<void> {
  try {
    // Strategy 1: Try to click close button using data attribute
    const closeButton = await page.$('[data-action="close"]');
    if (closeButton) {
      await closeButton.click();
      return;
    }

    // Strategy 2: Try to click cancel button using data attribute
    const cancelButton = await page.$('[data-action="cancel"]');
    if (cancelButton) {
      await cancelButton.click();
      return;
    }

    // Strategy 3: Press Escape key
    await page.keyboard.press('Escape');

    // Wait a moment for the modal to close
    await page.waitForTimeout(200);
  } catch (error) {
    console.log(`Failed to close modal: ${error.message}`);
    // Final fallback: just press escape
    await page.keyboard.press('Escape');
  }
}

/**
 * Submit/confirm modal action
 */
export async function confirmModal(page: Page): Promise<void> {
  await clickModalAction(page, 'confirm');
}

/**
 * Cancel modal action
 */
export async function cancelModal(page: Page): Promise<void> {
  await clickModalAction(page, 'cancel');
}

/**
 * Submit modal (for create/submit actions)
 */
export async function submitModal(page: Page): Promise<void> {
  await clickModalAction(page, 'submit');
}

/**
 * Complete modal interaction helper
 * Combines common patterns: wait for modal, fill inputs, submit
 */
export async function completeModalInteraction(
  page: Page,
  modalType: string,
  inputs: Record<string, string> = {},
  action: 'submit' | 'confirm' | 'cancel' = 'submit'
): Promise<void> {
  // Wait for the specific modal type
  const modal = await waitForModalType(page, modalType);
  if (!modal.found) {
    throw new Error(`Modal type "${modalType}" not found`);
  }

  // Fill any inputs
  for (const [inputName, value] of Object.entries(inputs)) {
    await fillModalInput(page, inputName, value);
  }

  // Perform the action
  await clickModalAction(page, action);
}

/**
 * Enhanced modal detection that checks for any modal presence
 */
export async function isModalOpen(page: Page): Promise<boolean> {
  return await page.evaluate(() => {
    const selectors = [
      '.modal-container',
      '.suggester-container',
      '.modal-backdrop'
    ];

    return selectors.some(selector => document.querySelector(selector) !== null);
  });
}

/**
 * Wait for modal to close
 */
export async function waitForModalToClose(page: Page, timeout: number = 5000): Promise<void> {
  await page.waitForFunction(
    () => {
      const selectors = [
        '.modal-container',
        '.suggester-container',
        '.modal-backdrop'
      ];

      return !selectors.some(selector => document.querySelector(selector) !== null);
    },
    {},
    { timeout }
  );
}
