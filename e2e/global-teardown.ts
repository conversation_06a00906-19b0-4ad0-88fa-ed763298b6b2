import { deleteAllGhostPosts } from './helpers/ghost-cleanup';
import { cleanupAllWorkerContexts } from './helpers/shared-context';

/**
 * Global teardown for all e2e tests
 * Cleans up test environments and Ghost posts
 */
export default async function globalTeardown() {
  console.log("🌍 Starting global e2e test teardown...");

  try {
    // Clean up all worker contexts first
    await cleanupAllWorkerContexts();

    // Clean up all posts from Ghost test instance
    await deleteAllGhostPosts();

    console.log("✅ Global e2e test teardown complete");

  } catch (error) {
    console.error("❌ Global teardown failed:", error);
    // Don't throw the error - teardown failures shouldn't break the test run
  }
}
