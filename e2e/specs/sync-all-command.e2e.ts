import {
  setupE2ETestHooks,
  executeCommand,
  expectNotice,
  expectPostFile
} from '../helpers/shared-context';
import { setupTestFailureHandler } from '../helpers/test-failure-handler';
import { TestPostManager } from '../helpers/ghost-api-helpers';
import { test, describe, beforeAll, afterEach } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';

// Setup screenshot capture on test failures
setupTestFailureHandler();

describe("Commands: Sync All", () => {
  const context = setupE2ETestHooks();
  let postManager: TestPostManager;

  beforeAll(async () => {
    postManager = new TestPostManager();
    if (!postManager.isAvailable()) {
      throw new Error('Ghost API not available - check credentials');
    }
  });

  afterEach(async () => {
    // Clean up managed posts
    await postManager.cleanup();

    // Clean up any local files created during the test
    const articlesDir = path.join(context.vaultPath, 'articles');
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.endsWith('.md')) {
          try {
            fs.unlinkSync(path.join(articlesDir, file));
          } catch (error) {
          }
        }
      }
    }
  });

  test("should sync all posts from Ghost using Sync All command", async () => {
    // Create test posts in Ghost using the simplified helper
    const testFixtures = [
      'sync-all-test-post-1.md',
      'sync-all-test-post-2.md',
      'sync-all-test-post-3.md'
    ];

    // Create all posts from fixtures using the new helper
    await postManager.createPostsFromFixtures(testFixtures);

    // Wait a moment for Ghost to process the posts
    await new Promise(resolve => setTimeout(resolve, 1000));

    await executeCommand(context, 'Ghost Sync: Sync all from Ghost');

    // Wait for the sync operation to complete with longer timeout for sync-all operations
    await expectNotice(context, "Successfully synced", 15000);



    // Verify each post was created as a local file with FULL content
    // Use the enhanced expectPostFile helper that waits for files to appear
    await expectPostFile(context, "sync-all-test-post-1", {
      title: "Sync All Test Post 1 - Comprehensive Content",
      slug: "sync-all-test-post-1",
      status: "published",
      visibility: "public",
      tags: ["test", "sync-all", "comprehensive"],
      content: "# Sync All Test Post 1 - Comprehensive Content",
      timeout: 15000
    });

    // Verify key content sections are present
    await expectPostFile(context, "sync-all-test-post-1", {
      content: "This is the first test post for the sync-all command functionality"
    });

    await expectPostFile(context, "sync-all-test-post-1", {
      content: "sync-all command should be able to handle posts"
    });

    // Verify second post with extended formatting
    await expectPostFile(context, "sync-all-test-post-2", {
      title: "Sync All Test Post 2 - Extended Formatting",
      slug: "sync-all-test-post-2",
      status: "published",
      visibility: "public",
      tags: ["test", "sync-all", "formatting"],
      content: "# Sync All Test Post 2 - Extended Formatting"
    });

    // Check for complex formatting elements
    await expectPostFile(context, "sync-all-test-post-2", {
      content: "**bold text**, *italic text*, ***bold and italic***"
    });

    // Verify third post with complex structure
    await expectPostFile(context, "sync-all-test-post-3", {
      title: "Sync All Test Post 3 - Complex Structure",
      slug: "sync-all-test-post-3",
      status: "published",
      visibility: "public",
      tags: ["test", "sync-all", "structure"],
      content: "# Sync All Test Post 3 - Complex Structure"
    });

    // Check for complex document structure
    await expectPostFile(context, "sync-all-test-post-3", {
      content: "complex document structure that includes multiple sections"
    });
  });
});
