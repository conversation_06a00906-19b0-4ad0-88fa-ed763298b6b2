<script lang="ts">
  import type { SyncStatus } from './types';

  export let status: SyncStatus;
  export let compact: boolean = false;

  $: badgeClass = compact ? 'ghost-sync-badge' : 'ghost-sync-status-badge';
  $: statusClass = compact
    ? `ghost-sync-badge-${status}`
    : `ghost-sync-status-badge-${getStatusType(status)}`;

  function getStatusType(status: SyncStatus): string {
    switch (status) {
      case 'synced':
        return 'success';
      case 'different':
        return 'warning';
      case 'new_post':
        return 'new';
      case 'unknown':
      default:
        return 'unknown';
    }
  }

  function getStatusText(status: SyncStatus): string {
    switch (status) {
      case 'synced':
        return compact ? '✓' : 'Synced';
      case 'different':
        return compact ? '!' : 'Different';
      case 'new_post':
        return compact ? '📝' : 'New';
      case 'unknown':
      default:
        return compact ? '?' : 'Unknown';
    }
  }
</script>

<span class="{badgeClass} {statusClass}">
  {getStatusText(status)}
</span>

<style>
  /* Component-specific styles can go here if needed */
  /* Most styles are inherited from the global CSS */
</style>
