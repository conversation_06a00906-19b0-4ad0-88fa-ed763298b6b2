export interface GhostSyncSettings {
	ghostUrl: string;
	ghostAdminApiKey: string;
	articlesDir: string;
	verbose: boolean;
}

export interface GhostPost {
	id?: string;
	title: string;
	slug: string;
	status: 'draft' | 'published' | 'scheduled';
	html?: string;
	lexical?: string;
	tags?: GhostTag[];
	primary_tag?: GhostTag;
	featured: boolean;
	visibility?: string;
	created_at: string;
	updated_at: string;
	published_at?: string;
	excerpt?: string;
	feature_image?: string;
	authors?: GhostAuthor[];
	newsletter?: GhostNewsletter;
	email?: GhostEmail;
}

export interface GhostTag {
	id?: string;
	name: string;
	slug: string;
}

export interface GhostAuthor {
	id?: string;
	name: string;
	slug: string;
	email?: string;
}

export interface GhostNewsletter {
	id: string;
	name: string;
	description?: string;
	slug: string;
	sender_name?: string;
	sender_email?: string;
	sender_reply_to: string;
	status: 'active' | 'archived';
	visibility: string;
	subscribe_on_signup: boolean;
	sort_order: number;
	header_image?: string;
	show_header_icon: boolean;
	show_header_title: boolean;
	title_font_category: string;
	title_alignment: string;
	show_feature_image: boolean;
	body_font_category: string;
	footer_content?: string;
	show_badge: boolean;
	created_at: string;
	updated_at: string;
	show_header_name: boolean;
	uuid: string;
}

export interface GhostEmail {
	id: string;
	uuid: string;
	status: 'submitted' | 'failed' | 'pending';
	recipient_filter: string;
	error?: string;
	error_data?: string;
	email_count: number;
	delivered_count: number;
	opened_count: number;
	failed_count: number;
	subject: string;
	from: string;
	reply_to: string;
	html?: string;
	plaintext?: string;
	track_opens: boolean;
	submitted_at?: string;
	created_at: string;
	updated_at: string;
}

export interface ArticleFrontMatter {
	title?: string;
	Title?: string;
	slug?: string;
	Slug?: string;
	status?: 'draft' | 'published' | 'scheduled';
	Status?: 'draft' | 'published' | 'scheduled';
	tags?: string[];
	Tags?: string[];
	featured?: boolean;
	Featured?: boolean;
	'Created At'?: string;
	'created_at'?: string;
	'Updated At'?: string;
	'updated_at'?: string;
	'Published At'?: string;
	'published_at'?: string;
	// REMOVED: Synced At and Changed At are now stored in SyncMetadataStorage, not frontmatter
	'Feature Image'?: string;
	'feature_image'?: string;
	excerpt?: string;
	Excerpt?: string;
	newsletter?: string;
	Newsletter?: string;
}

export type SyncStatus = 'synced' | 'different' | 'unknown';

export interface SyncStatusData {
	title: SyncStatus;
	slug: SyncStatus;
	status: SyncStatus;
	tags: SyncStatus;
	featured: SyncStatus;
	feature_image: SyncStatus;
	visibility: SyncStatus;
	primary_tag: SyncStatus;
	created_at: SyncStatus;
	updated_at: SyncStatus;
	published_at: SyncStatus;
	synced_at: SyncStatus;
	newsletter: SyncStatus;
	email_sent: SyncStatus;
	ghostPost?: GhostPost;
}

export interface ParsedArticle {
	frontMatter: ArticleFrontMatter;
	markdownContent: string;
}

export enum SyncDecision {
	SYNC_TO_GHOST = 'sync_to_ghost',
	SYNC_FROM_GHOST = 'sync_from_ghost',
	CONFLICT = 'conflict',
	NO_SYNC_NEEDED = 'no_sync_needed'
}

export interface LocalPost {
	/** The frontmatter properties */
	frontMatter: ArticleFrontMatter;
	/** The markdown content */
	content: string;
	/** When this post was last synced TO Ghost (ISO string) */
	syncedAt?: string;
	/** When the content or synced properties were last changed (ISO string) */
	changedAt?: string;
	/** File system modification time (timestamp) */
	fileModifiedAt: number;
	/** File path for reference */
	filePath: string;
}

export interface SyncAnalysis {
	decision: SyncDecision;
	localPost: LocalPost;
	ghostPost?: GhostPost;
	reason: string;
	lastSyncTime?: string;
	ghostUpdatedTime?: string;
	localChangedTime?: string;
}
