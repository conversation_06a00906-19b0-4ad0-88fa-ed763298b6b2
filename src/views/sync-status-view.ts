import { <PERSON>pace<PERSON><PERSON><PERSON>, TFile, Notice } from "obsidian";
import type GhostSyncPlugin from "../main";
import type { GhostPost } from "../types";
import { ContentConverter } from "../utils/content-converter";
import { SvelteView } from "../components/SvelteView";
import GhostSyncView from "../components/GhostSyncView.svelte";
import PublishDialog from "../components/PublishDialog.svelte";
import PostBrowser from "../components/PostBrowser.svelte";
import type { SyncStatusData, PublishOptions } from "../components/types";
import { ObsidianGhostAPI } from "../api/ghost-api";
import { SyncStatusService } from "../services/sync-status-service";
import { SmartSyncService } from "../services/smart-sync-service";
import { SyncDecision } from "../types";
import { ObsidianAppAdapter } from "../services/obsidian-app-adapter";
import { PropertyMapper } from "../utils/property-mapping";
import * as path from "path";
import * as crypto from "crypto";

export const VIEW_TYPE_GHOST_SYNC_STATUS = 'ghost-sync-status';

export class SvelteSyncStatusView extends SvelteView {
  private currentFile: TFile | null = null;
  private defaultSyncStatus: SyncStatusData = {
    title: 'unknown',
    slug: 'unknown',
    status: 'unknown',
    tags: 'unknown',
    featured: 'unknown',
    feature_image: 'unknown',
    visibility: 'unknown',
    primary_tag: 'unknown',
    created_at: 'unknown',
    updated_at: 'unknown',
    published_at: 'unknown',
    synced_at: 'unknown',
    newsletter: 'unknown',
    email_sent: 'unknown'
  };
  private syncStatus: SyncStatusData = { ...this.defaultSyncStatus };

  private publishDialog: PublishDialog | null = null;
  private postBrowser: PostBrowser | null = null;
  private syncStatusService: SyncStatusService | null;
  private smartSyncService: SmartSyncService | null;
  private appAdapter: ObsidianAppAdapter;

  constructor(leaf: WorkspaceLeaf, plugin: GhostSyncPlugin, syncStatusService?: SyncStatusService | null) {
    super(leaf, plugin);

    // Use the plugin's appAdapter instead of creating a new one
    this.appAdapter = plugin.appAdapter;

    if (syncStatusService === null) {
      // Null indicates that API services should not be created due to invalid configuration
      this.syncStatusService = null;
      this.smartSyncService = null;
    } else if (syncStatusService) {
      this.syncStatusService = syncStatusService;
      // Still need to create SmartSyncService
      try {
        const ghostAPI = new ObsidianGhostAPI(plugin.settings.ghostUrl, plugin.settings.ghostAdminApiKey);
        this.smartSyncService = new SmartSyncService({
          ghostAPI,
          readFile: (file: TFile) => plugin.appAdapter.readFile(file),
          writeFile: (file: TFile, content: string) => plugin.appAdapter.writeFile(file, content),
          parseMarkdown: (content: string, file?: TFile) => {
            if (!file) {
              throw new Error('File parameter is required for parseMarkdown in Obsidian plugin');
            }
            const parsed = ContentConverter.parseArticle(content, file, plugin.app);
            return { frontMatter: parsed.frontMatter, content: parsed.markdownContent };
          },
          syncMetadata: plugin.syncMetadata,
          renameFileForTitleChange: (file: TFile, newTitle: string) => plugin.renameFileForTitleChange(file, newTitle)
        });
      } catch (error) {
        console.warn('Failed to create SmartSyncService:', error);
        this.smartSyncService = null;
      }
    } else {
      // Create default services with error handling
      try {
        const ghostAPI = new ObsidianGhostAPI(plugin.settings.ghostUrl, plugin.settings.ghostAdminApiKey);
        this.syncStatusService = new SyncStatusService({
          ghostAPI,
          appAdapter: this.appAdapter,
          syncMetadata: plugin.syncMetadata
        });

        this.smartSyncService = new SmartSyncService({
          ghostAPI,
          readFile: (file: TFile) => plugin.appAdapter.readFile(file),
          writeFile: (file: TFile, content: string) => plugin.appAdapter.writeFile(file, content),
          parseMarkdown: (content: string, file?: TFile) => {
            if (!file) {
              throw new Error('File parameter is required for parseMarkdown in Obsidian plugin');
            }
            const parsed = ContentConverter.parseArticle(content, file, plugin.app);
            return { frontMatter: parsed.frontMatter, content: parsed.markdownContent };
          },
          syncMetadata: plugin.syncMetadata,
          renameFileForTitleChange: (file: TFile, newTitle: string) => plugin.renameFileForTitleChange(file, newTitle)
        });
      } catch (error) {
        console.warn('Failed to create Ghost API services:', error);
        this.syncStatusService = null;
        this.smartSyncService = null;
      }
    }
  }

  getViewType() {
    return VIEW_TYPE_GHOST_SYNC_STATUS;
  }

  getDisplayText() {
    return 'Ghost';
  }

  getIcon() {
    return 'sync';
  }

  async onOpen() {
    await super.onOpen();

    // Listen for active file changes
    this.registerEvent(
      this.app.workspace.on('active-leaf-change', () => {
        console.log('Ghost tab: active-leaf-change event fired');
        this.updateCurrentFile();
      })
    );

    // Listen for file open events
    this.registerEvent(
      this.app.workspace.on('file-open', (file) => {
        console.log('Ghost tab: file-open event fired for:', file?.path);
        this.updateCurrentFile();
      })
    );

    // Listen for layout changes (more comprehensive)
    this.registerEvent(
      this.app.workspace.on('layout-change', () => {
        console.log('Ghost tab: layout-change event fired');
        this.updateCurrentFile();
      })
    );

    // Listen for file changes
    this.registerEvent(
      this.app.vault.on('modify', async (file) => {
        if (file === this.currentFile && file instanceof TFile) {
          console.log('Ghost tab: file modified:', file.path);
          await this.handleFileModification(file);
          this.updateSyncStatus();
        }
      })
    );

    // Listen for editor changes (more reliable for file switching)
    this.registerEvent(
      this.app.workspace.on('editor-change', (editor, info) => {
        console.log('Ghost tab: editor-change event fired');
        this.updateCurrentFile();
      })
    );

    console.log('Ghost tab: Initial setup complete, calling updateCurrentFile');
    this.updateCurrentFile();
  }

  protected createComponent(container: HTMLElement) {
    const component = this.createSvelteComponent(
      GhostSyncView,
      container,
      {
        currentFile: this.currentFile,
        syncStatus: this.syncStatus
      }
    );

    // Listen to component events
    component.$on('smartSync', async () => {
      await this.smartSync();
    });

    component.$on('publish', () => {
      this.showPublishDialog();
    });

    component.$on('browsePosts', () => {
      this.showPostBrowser();
    });

    return component;
  }

  private recreateComponent() {
    if (this.component) {
      // Destroy the old component
      this.component.$destroy();
      this.component = null;
    }

    // Create a new component with updated props
    const container = this.contentEl;
    container.empty();
    container.addClass('svelte-view-container');
    this.component = this.createComponent(container);
  }

  private updateCurrentFile() {
    const activeFile = this.app.workspace.getActiveFile();
    console.log('Ghost tab: updateCurrentFile called');
    console.log('Ghost tab: activeFile:', activeFile?.path);
    console.log('Ghost tab: currentFile:', this.currentFile?.path);

    if (activeFile !== this.currentFile) {
      console.log('Ghost tab: File changed, updating view');
      this.currentFile = activeFile;
      this.updateSyncStatus();

      // In Svelte 5, we need to recreate the component with new props
      // since $set is deprecated and bindable props should be used
      this.recreateComponent();
    } else {
      console.log('Ghost tab: No file change detected');
    }
  }



  private contentHashes = new Map<string, string>();
  private isUpdatingTimestamp = false;
  private isSyncing = false;

  /**
   * Calculate MD5 hash of content that matters for sync
   */
  private calculateSyncContentHash(content: string, file: TFile): string {
    try {
      const parsed = ContentConverter.parseArticle(content, file, this.app);

      // Create a string that represents all sync-relevant content
      const syncedProperties = PropertyMapper.getSyncProperties();
      const syncedValues: any = {};

      for (const mapping of syncedProperties) {
        const value = PropertyMapper.extractObsidianValue(parsed.frontMatter, mapping);
        if (value !== undefined) {
          syncedValues[mapping.obsidianProperty] = value;
        }
      }

      // Combine synced properties and content
      const syncContent = {
        properties: syncedValues,
        content: parsed.markdownContent.trim()
      };

      return crypto.createHash('md5').update(JSON.stringify(syncContent)).digest('hex');
    } catch (error) {
      // Fallback to simple content hash if parsing fails
      return crypto.createHash('md5').update(content || '').digest('hex');
    }
  }

  private async handleFileModification(file: TFile) {
    console.log('Ghost tab: handleFileModification called for:', file.path);
    console.log('Ghost tab: isUpdatingTimestamp:', this.isUpdatingTimestamp);

    // Prevent infinite loops when we update the timestamp
    if (this.isUpdatingTimestamp) {
      console.log('Ghost tab: Skipping - isUpdatingTimestamp is true');
      return;
    }

    try {
      // Read the current content
      const currentContent = await this.appAdapter.readFile(file);

      // Parse the content to check if it has frontmatter and a slug
      const parsed = ContentConverter.parseArticle(currentContent, file, this.app);
      const slug = parsed.frontMatter.slug || parsed.frontMatter.Slug;

      // Only track files that have a slug (are meant to be synced)
      if (!slug) {
        console.log('Ghost tab: Skipping - no slug found');
        return;
      }

      // Calculate hash of sync-relevant content
      const currentHash = this.calculateSyncContentHash(currentContent, file);
      const previousHash = this.contentHashes.get(file.path);

      console.log('Ghost tab: Hash comparison for:', file.path);
      console.log('Ghost tab: previousHash:', previousHash);
      console.log('Ghost tab: currentHash:', currentHash);

      if (previousHash && currentHash !== previousHash) {
        console.log('Ghost tab: Hash changed - updating internal metadata');

        // Sync-relevant content has changed, update the changed_at timestamp in metadata storage
        console.log('Ghost tab: Sync-relevant content changed, updating metadata for:', file.path);
        await this.plugin.syncMetadata.markAsChanged(file);

        // Update hash with the current content
        this.contentHashes.set(file.path, currentHash);
      } else if (!previousHash) {
        // First time seeing this file, just cache the hash
        console.log('Ghost tab: First time seeing file, caching hash');
        this.contentHashes.set(file.path, currentHash);
      } else {
        // Content hasn't changed in a sync-relevant way, just update the hash
        console.log('Ghost tab: Content unchanged, updating hash');
        this.contentHashes.set(file.path, currentHash);
      }
    } catch (error) {
      console.error('Error handling file modification:', error);
      this.isUpdatingTimestamp = false;
    }
  }

  private async updateSyncStatus() {
    console.log('Ghost tab: updateSyncStatus called');
    if (!this.currentFile) {
      console.log('Ghost tab: No current file, skipping sync status update');
      return;
    }

    console.log('Ghost tab: Updating sync status for file:', this.currentFile.path);

    try {
      // Initialize hash cache for this file if we haven't seen it before
      if (!this.contentHashes.has(this.currentFile.path)) {
        try {
          const content = await this.appAdapter.readFile(this.currentFile);
          const hash = this.calculateSyncContentHash(content, this.currentFile);
          this.contentHashes.set(this.currentFile.path, hash);
        } catch (error) {
          console.error('Error initializing content hash:', error);
          // Continue without hash caching if there's an error
        }
      }

      // Use the service to calculate sync status
      if (this.syncStatusService) {
        this.syncStatus = await this.syncStatusService.calculateSyncStatus(this.currentFile);
      } else {
        // If no service available, show configuration error status
        this.syncStatus = {
          ...this.defaultSyncStatus,
          title: 'unknown',
          slug: 'unknown'
        };
      }

      // In Svelte 5, recreate component with updated props
      this.recreateComponent();

    } catch (error) {
      console.error('Error updating sync status:', error);
      this.resetSyncStatus();
    }
  }



  private resetSyncStatus() {
    this.syncStatus = {
      title: 'unknown',
      slug: 'unknown',
      status: 'unknown',
      tags: 'unknown',
      featured: 'unknown',
      feature_image: 'unknown',
      visibility: 'unknown',
      primary_tag: 'unknown',
      created_at: 'unknown',
      updated_at: 'unknown',
      published_at: 'unknown',
      synced_at: 'unknown',
      newsletter: 'unknown',
      email_sent: 'unknown'
    };

    this.recreateComponent();
  }

  private async smartSync() {
    if (!this.currentFile) {
      new Notice('No file selected');
      return;
    }

    if (!this.smartSyncService) {
      new Notice('Ghost Sync not configured. Please check plugin settings.');
      return;
    }

    try {
      // Parse local post
      const localPost = await this.smartSyncService.parseLocalPost(this.currentFile);

      // Check if post has a slug
      const slug = localPost.frontMatter.slug || localPost.frontMatter.Slug;
      if (!slug) {
        new Notice('Post must have a slug to sync');
        return;
      }

      // Use UUID-based lookup only - no more slug-based lookups
      const ghostPost = await this.smartSyncService.findGhostPost(this.currentFile, localPost);

      // Analyze what sync action is needed
      const analysis = await this.smartSyncService.analyzeSyncNeeded(localPost, ghostPost);

      // Execute sync based on analysis
      switch (analysis.decision) {
        case SyncDecision.SYNC_TO_GHOST:
          new Notice(`${analysis.reason} - syncing to Ghost...`);
          // Set flag to prevent file modification handler from interfering with sync timestamps
          this.isUpdatingTimestamp = true;
          try {
            // Pass the already-fetched ghost post to avoid duplicate API calls
            const result = await this.smartSyncService.syncToGhost(localPost, this.currentFile, ghostPost);
            // Update sync metadata after successful sync to Ghost
            await this.plugin.syncMetadata.setSyncedAt(this.currentFile, result.updated_at);
            new Notice('Synced to Ghost successfully');
          } finally {
            // Clear the flag after sync is complete
            this.isUpdatingTimestamp = false;
          }
          break;

        case SyncDecision.SYNC_FROM_GHOST:
          new Notice(`${analysis.reason} - syncing from Ghost...`);
          if (ghostPost) {
            // Set flag to prevent file modification handler from interfering with sync timestamps
            this.isUpdatingTimestamp = true;
            try {
              await this.smartSyncService.syncFromGhost(this.currentFile, ghostPost);
              new Notice('Synced from Ghost successfully');
            } finally {
              // Clear the flag after sync is complete
              this.isUpdatingTimestamp = false;
            }
          }
          break;

        case SyncDecision.CONFLICT:
          new Notice(`Conflict detected: ${analysis.reason}`);
          await this.handleSyncConflict(analysis);
          break;

        case SyncDecision.NO_SYNC_NEEDED:
          new Notice('No sync needed - everything is up to date');
          break;
      }

      // Refresh sync status
      setTimeout(() => this.updateSyncStatus(), 1000);

    } catch (error) {
      console.error('Smart sync failed:', error);
      new Notice(`Sync failed: ${error.message}`);
    }
  }

  private async handleSyncConflict(analysis: any) {
    if (!this.smartSyncService) {
      new Notice('Ghost Sync not configured. Please check plugin settings.');
      return;
    }

    // For now, ask user which direction to sync
    const choice = await this.showConflictDialog(analysis);

    if (choice === 'to_ghost') {
      new Notice('Syncing local changes to Ghost...');
      // Set flag to prevent file modification handler from interfering with sync timestamps
      this.isUpdatingTimestamp = true;
      try {
        const result = await this.smartSyncService.syncToGhost(analysis.localPost, this.currentFile);
        // Update sync metadata after successful sync to Ghost
        await this.plugin.syncMetadata.setSyncedAt(this.currentFile, result.updated_at);
        new Notice('Synced to Ghost successfully');
      } finally {
        // Clear the flag after sync is complete
        this.isUpdatingTimestamp = false;
      }
    } else if (choice === 'from_ghost') {
      new Notice('Syncing Ghost changes to local...');
      if (analysis.ghostPost) {
        // Set flag to prevent file modification handler from interfering with sync timestamps
        this.isUpdatingTimestamp = true;
        try {
          await this.smartSyncService.syncFromGhost(this.currentFile, analysis.ghostPost);
          new Notice('Synced from Ghost successfully');
        } finally {
          // Clear the flag after sync is complete
          this.isUpdatingTimestamp = false;
        }
      }
    }
  }

  private async showConflictDialog(analysis: any): Promise<'to_ghost' | 'from_ghost' | 'cancel'> {
    return new Promise((resolve) => {
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
      `;

      const content = document.createElement('div');
      content.style.cssText = `
        background: var(--background-primary);
        padding: 20px;
        border-radius: 8px;
        max-width: 500px;
        text-align: center;
      `;

      content.innerHTML = `
        <h2>Sync Conflict Detected</h2>
        <p>${analysis.reason}</p>
        <p>Both the local file and Ghost post have been modified since the last sync.</p>
        <p>Which version would you like to keep?</p>
        <div style="margin-top: 20px;">
          <button id="use-local" style="margin: 5px;">Use Local (sync to Ghost)</button>
          <button id="use-ghost" style="margin: 5px;">Use Ghost (sync from Ghost)</button>
          <button id="cancel" style="margin: 5px;">Cancel</button>
        </div>
      `;

      modal.appendChild(content);
      document.body.appendChild(modal);

      content.querySelector('#use-local')?.addEventListener('click', () => {
        document.body.removeChild(modal);
        resolve('to_ghost');
      });

      content.querySelector('#use-ghost')?.addEventListener('click', () => {
        document.body.removeChild(modal);
        resolve('from_ghost');
      });

      content.querySelector('#cancel')?.addEventListener('click', () => {
        document.body.removeChild(modal);
        resolve('cancel');
      });
    });
  }

  private showPublishDialog() {
    if (!this.syncStatus.ghostPost) {
      new Notice('No Ghost post to publish');
      return;
    }

    // Create modal container
    const modalContainer = document.body.createDiv();

    this.publishDialog = new PublishDialog({
      target: modalContainer,
      props: {
        ghostPost: this.syncStatus.ghostPost,
        show: true
      },
      context: new Map([
        ['ghost-sync-plugin', { plugin: this.plugin }]
      ])
    });

    this.publishDialog.$on('confirm', async (event) => {
      await this.handlePublish(event.detail);
      this.publishDialog?.$destroy();
      modalContainer.remove();
    });

    this.publishDialog.$on('cancel', () => {
      this.publishDialog?.$destroy();
      modalContainer.remove();
    });
  }

  private showPostBrowser() {
    // Create modal container
    const modalContainer = document.body.createDiv();

    this.postBrowser = new PostBrowser({
      target: modalContainer,
      props: {
        show: true
      },
      context: new Map([
        ['ghost-sync-plugin', { plugin: this.plugin }]
      ])
    });

    this.postBrowser.$on('select', async (event) => {
      await this.handlePostSelection(event.detail);
      this.postBrowser?.$destroy();
      modalContainer.remove();
    });

    this.postBrowser.$on('cancel', () => {
      this.postBrowser?.$destroy();
      modalContainer.remove();
    });
  }

  private async handlePublish(options: PublishOptions) {
    if (!this.syncStatus.ghostPost) {
      new Notice('No Ghost post to publish');
      return;
    }

    try {
      const ghostAPI = new ObsidianGhostAPI(this.plugin.settings.ghostUrl, this.plugin.settings.ghostAdminApiKey);

      const publishOptions: any = {
        action: options.action,
        newsletter: options.newsletter?.slug,
        emailSegment: options.emailSegment,
        testMode: options.testMode
      };

      // Add scheduling if specified
      if (options.scheduleType === 'scheduled' && options.scheduledDate) {
        publishOptions.scheduledDate = options.scheduledDate;
      }

      const result = await ghostAPI.publishPost(this.syncStatus.ghostPost, publishOptions);

      if (options.scheduleType === 'scheduled') {
        new Notice(`Post scheduled for publication at ${new Date(options.scheduledDate!).toLocaleString()}`);
      } else {
        new Notice(`Post published successfully with action: ${options.action}`);
      }

      // Refresh the sync status to show updated information
      await this.updateSyncStatus();
    } catch (error) {
      console.error('Error publishing post:', error);
      new Notice(`Error publishing post: ${error.message}`);
    }
  }

  private async handlePostSelection(post: GhostPost) {
    if (!this.smartSyncService) {
      new Notice('Ghost Sync not configured. Please check plugin settings.');
      return;
    }

    try {
      new Notice(`Syncing "${post.title}" from Ghost...`);

      const filename = post.slug + '.md';

      // Set flag to prevent file modification handler from interfering with sync timestamps
      this.isUpdatingTimestamp = true;

      // Ensure articles directory exists
      await this.appAdapter.ensureArticlesDirExists();

      const existingFile = this.appAdapter.getArticleFile(filename);
      let targetFile: TFile;

      if (existingFile) {
        targetFile = existingFile;
      } else {
        // Create the file first with minimal content to get a TFile object
        const tempContent = `---\ntitle: "${post.title}"\nslug: "${post.slug}"\n---\n\n# ${post.title}\n\nLoading...`;
        targetFile = await this.appAdapter.createArticleFile(filename, tempContent);
      }

      // Use the proper sync method that handles timestamps correctly
      await this.smartSyncService.syncFromGhost(targetFile, post);

      // Clear the flag after sync is complete
      this.isUpdatingTimestamp = false;

      new Notice(`Synced "${post.title}" from Ghost`);

      // Open the synced file in the editor
      const leaf = this.app.workspace.getLeaf();
      await leaf.openFile(targetFile);

      // Focus the editor
      this.app.workspace.setActiveLeaf(leaf);
    } catch (error) {
      console.error("Error syncing selected post:", error);
      new Notice(`Error syncing post: ${error.message}`);
      // Make sure to clear the flag even if there's an error
      this.isUpdatingTimestamp = false;
    }
  }
}
