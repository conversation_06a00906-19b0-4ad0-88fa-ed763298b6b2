/**
 * Sync metadata storage service using Obsidian's plugin data storage
 * This stores internal sync timestamps separately from frontmatter
 */

import type { Plugin, TFile } from 'obsidian';

export interface SyncMetadata {
  changed_at?: string;
  synced_at?: string;
  ghost_uuid?: string;
  ghost_data?: {
    primary_tag?: string | null;
  };
}

export interface UuidMapping {
  [uuid: string]: string; // uuid -> filePath
}

export interface SyncMetadataStore {
  [filePath: string]: SyncMetadata;
}

export interface PluginDataStore {
  'sync-metadata'?: SyncMetadataStore;
  'uuid-mappings'?: UuidMapping;
}

export class SyncMetadataStorage {
  private plugin: Plugin;
  private metadata: SyncMetadataStore = {};
  private uuidMappings: UuidMapping = {};
  private readonly STORAGE_KEY = 'sync-metadata';
  private readonly UUID_MAPPINGS_KEY = 'uuid-mappings';

  constructor(plugin: Plugin) {
    this.plugin = plugin;
  }

  /**
   * Load metadata from plugin storage
   */
  async load(): Promise<void> {
    try {
      const data: PluginDataStore = await this.plugin.loadData() || {};
      this.metadata = data[this.STORAGE_KEY] || {};
      this.uuidMappings = data[this.UUID_MAPPINGS_KEY] || {};
    } catch (error) {
      console.error('Error loading sync metadata:', error);
      this.metadata = {};
      this.uuidMappings = {};
    }
  }

  /**
   * Save metadata to plugin storage
   */
  async save(): Promise<void> {
    try {
      const data: PluginDataStore = await this.plugin.loadData() || {};
      data[this.STORAGE_KEY] = this.metadata;
      data[this.UUID_MAPPINGS_KEY] = this.uuidMappings;
      await this.plugin.saveData(data);
    } catch (error) {
      console.error('Error saving sync metadata:', error);
    }
  }

  /**
   * Set the changed_at timestamp for a file
   */
  async setChangedAt(file: TFile, timestamp: string): Promise<void> {
    const filePath = file.path;
    if (!this.metadata[filePath]) {
      this.metadata[filePath] = {};
    }
    this.metadata[filePath].changed_at = timestamp;
    await this.save();
  }

  /**
   * Set the synced_at timestamp for a file
   */
  async setSyncedAt(file: TFile, timestamp: string): Promise<void> {
    const filePath = file.path;
    if (!this.metadata[filePath]) {
      this.metadata[filePath] = {};
    }
    this.metadata[filePath].synced_at = timestamp;
    await this.save();
  }

  /**
   * Get sync metadata for a file
   */
  getMetadata(file: TFile): SyncMetadata {
    return this.metadata[file.path] || {};
  }

  /**
   * Get changed_at timestamp for a file
   */
  getChangedAt(file: TFile): string | undefined {
    return this.metadata[file.path]?.changed_at;
  }

  /**
   * Get stored Primary Tag for a file (from Ghost data, not frontmatter)
   */
  getStoredPrimaryTag(file: TFile): string | null {
    return this.metadata[file.path]?.ghost_data?.primary_tag || null;
  }

  /**
   * Get synced_at timestamp for a file
   */
  getSyncedAt(file: TFile): string | undefined {
    return this.metadata[file.path]?.synced_at;
  }

  /**
   * Clear metadata for a file (when file is deleted)
   */
  async clearMetadata(file: TFile): Promise<void> {
    delete this.metadata[file.path];
    await this.save();
  }

  /**
   * Update changed_at when file content changes
   */
  async markAsChanged(file: TFile): Promise<void> {
    await this.setChangedAt(file, new Date().toISOString());
  }

  /**
   * Mark file as synced
   */
  async markAsSynced(file: TFile): Promise<void> {
    await this.setSyncedAt(file, new Date().toISOString());
  }

  /**
   * Set both timestamps when syncing from Ghost
   */
  async setSyncFromGhost(file: TFile, ghostUpdatedAt: string, ghostPost?: any): Promise<void> {
    const filePath = file.path;
    const syncTime = new Date().toISOString();

    if (!this.metadata[filePath]) {
      this.metadata[filePath] = {};
    }

    this.metadata[filePath].changed_at = ghostUpdatedAt;
    this.metadata[filePath].synced_at = syncTime;

    // Store Ghost post data that shouldn't be in frontmatter but needs to be compared
    if (ghostPost) {
      this.metadata[filePath].ghost_data = {
        primary_tag: ghostPost.primary_tag?.name || null
      };
    }

    await this.save();
  }

  /**
   * Set the Ghost UUID for a file
   */
  async setGhostUuid(file: TFile, uuid: string): Promise<void> {
    const filePath = file.path;

    // Remove any existing mapping for this UUID
    const existingFilePath = this.uuidMappings[uuid];
    if (existingFilePath && existingFilePath !== filePath) {
      // Clear the UUID from the old file's metadata
      if (this.metadata[existingFilePath]) {
        delete this.metadata[existingFilePath].ghost_uuid;
      }
    }

    // Set up the new mapping
    this.uuidMappings[uuid] = filePath;

    if (!this.metadata[filePath]) {
      this.metadata[filePath] = {};
    }
    this.metadata[filePath].ghost_uuid = uuid;

    await this.save();
  }

  /**
   * Get the Ghost UUID for a file
   */
  getGhostUuid(file: TFile): string | undefined {
    return this.metadata[file.path]?.ghost_uuid;
  }

  /**
   * Get the file path for a Ghost UUID
   */
  getFilePathByUuid(uuid: string): string | undefined {
    return this.uuidMappings[uuid];
  }

  /**
   * Get the TFile for a Ghost UUID
   */
  getFileByUuid(uuid: string): TFile | null {
    const filePath = this.uuidMappings[uuid];
    if (!filePath) {
      return null;
    }

    // We need access to the app to get the file, but we only have the plugin
    // The calling code should handle this conversion
    return null;
  }

  /**
   * Remove UUID mapping when file is deleted or UUID changes
   */
  async clearUuidMapping(uuid: string): Promise<void> {
    const filePath = this.uuidMappings[uuid];
    if (filePath && this.metadata[filePath]) {
      delete this.metadata[filePath].ghost_uuid;
    }
    delete this.uuidMappings[uuid];
    await this.save();
  }

  /**
   * Update file path in UUID mapping when file is renamed
   */
  async updateFilePath(oldPath: string, newPath: string): Promise<void> {
    // Find UUID for the old path
    const metadata = this.metadata[oldPath];
    if (!metadata?.ghost_uuid) {
      return;
    }

    const uuid = metadata.ghost_uuid;

    // Update the UUID mapping
    this.uuidMappings[uuid] = newPath;

    // Move metadata to new path
    this.metadata[newPath] = metadata;
    delete this.metadata[oldPath];

    await this.save();
  }

  /**
   * Get all UUID mappings (for debugging/migration)
   */
  getAllUuidMappings(): UuidMapping {
    return { ...this.uuidMappings };
  }
}
