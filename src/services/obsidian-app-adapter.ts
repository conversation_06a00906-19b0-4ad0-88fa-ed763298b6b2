import type { App, TFile } from "obsidian";
import type { AppAdapter, FileMetadata } from "./sync-status-service";
import * as path from "path";

export class ObsidianAppAdapter implements AppAdapter {
  private app: App;
  private articlesDir: string;

  constructor(app: App, articlesDir: string = "articles") {
    this.app = app;
    this.articlesDir = articlesDir;
  }

  async readFile(file: TFile): Promise<string> {
    return await this.app.vault.read(file);
  }

  async writeFile(file: TFile, content: string): Promise<void> {
    return await this.app.vault.modify(file, content);
  }

  async createFile(filePath: string, content: string): Promise<TFile> {
    return await this.app.vault.create(filePath, content) as TFile;
  }

  getFile(filePath: string): TFile | null {
    const file = this.app.vault.getAbstractFileByPath(filePath);
    return file as TFile | null;
  }

  getFileMetadata(file: TFile): FileMetadata | null {
    try {
      const cache = this.app.metadataCache.getFileCache(file);
      return {
        frontmatter: cache?.frontmatter,
        content: '' // We'll read content separately when needed
      };
    } catch (error) {
      console.error('Error getting file metadata:', error);
      return null;
    }
  }

  /**
   * Get file metadata with retry logic for cases where metadata cache is updating
   */
  async getFileMetadataWithRetry(file: TFile, maxRetries: number = 3, delay: number = 100): Promise<FileMetadata | null> {
    for (let i = 0; i < maxRetries; i++) {
      const metadata = this.getFileMetadata(file);

      if (metadata?.frontmatter) {
        return metadata;
      }

      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return this.getFileMetadata(file);
  }

  /**
   * Get the configured articles directory path
   */
  getArticlesDir(): string {
    return this.articlesDir;
  }

  /**
   * Update the articles directory configuration
   */
  setArticlesDir(articlesDir: string): void {
    this.articlesDir = articlesDir;
  }

  /**
   * Get the full path for a file within the articles directory
   */
  getArticleFilePath(filename: string): string {
    return path.posix.join(this.articlesDir, filename);
  }

  /**
   * Check if a file is within the articles directory
   */
  isFileInArticlesDir(file: TFile): boolean {
    const normalizedArticlesPath = path.normalize(this.articlesDir);
    const normalizedFilePath = path.normalize(file.path);
    return normalizedFilePath.startsWith(normalizedArticlesPath);
  }

  /**
   * Ensure the articles directory exists
   */
  async ensureArticlesDirExists(): Promise<void> {
    try {
      await this.app.vault.createFolder(this.articlesDir);
    } catch (error) {
      // Folder might already exist, ignore error
    }
  }

  /**
   * Create a file in the articles directory
   */
  async createArticleFile(filename: string, content: string): Promise<TFile> {
    await this.ensureArticlesDirExists();
    const filePath = this.getArticleFilePath(filename);
    return await this.createFile(filePath, content);
  }

  /**
   * Get a file from the articles directory
   */
  getArticleFile(filename: string): TFile | null {
    const filePath = this.getArticleFilePath(filename);
    return this.getFile(filePath);
  }
}
