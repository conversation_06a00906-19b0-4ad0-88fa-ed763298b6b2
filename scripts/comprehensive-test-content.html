<h1>Obsidian Ghost Sync - Formatting Test</h1>

<p>This post contains all formatting elements we want to support.</p>

<h2>Text Formatting</h2>

<p>Here we test <strong>bold text</strong>, <em>italic text</em>, <strong><em>bold italic</em></strong>, <del>strikethrough</del>, and <code>inline code</code>.</p>

<h2>Headings</h2>

<h3>H3 Heading</h3>
<h4>H4 Heading</h4>
<h5>H5 Heading</h5>
<h6>H6 Heading</h6>

<h2>Lists</h2>

<h3>Unordered List</h3>
<ul>
<li>First item</li>
<li>Second item
<ul>
<li>Nested item</li>
<li>Another nested item</li>
</ul>
</li>
<li>Third item</li>
</ul>

<h3>Ordered List</h3>
<ol>
<li>First numbered item</li>
<li>Second numbered item
<ol>
<li>Nested numbered item</li>
<li>Another nested numbered item</li>
</ol>
</li>
<li>Third numbered item</li>
</ol>

<h2>Callouts</h2>

<h3>Note Callout</h3>
<div class="kg-card kg-callout-card kg-callout-card-blue">
<div class="kg-callout-emoji">📝</div>
<div class="kg-callout-text">This is a note callout with important information.</div>
</div>

<h3>Info Callout</h3>
<div class="kg-card kg-callout-card kg-callout-card-blue">
<div class="kg-callout-emoji">ℹ️</div>
<div class="kg-callout-text">This is an info callout with additional details.</div>
</div>

<h3>Tip Callout</h3>
<div class="kg-card kg-callout-card kg-callout-card-green">
<div class="kg-callout-emoji">💡</div>
<div class="kg-callout-text">This is a tip callout with helpful advice.</div>
</div>

<h3>Success Callout</h3>
<div class="kg-card kg-callout-card kg-callout-card-green">
<div class="kg-callout-emoji">✅</div>
<div class="kg-callout-text">This is a success callout indicating completion.</div>
</div>

<h3>Warning Callout</h3>
<div class="kg-card kg-callout-card kg-callout-card-yellow">
<div class="kg-callout-emoji">⚠️</div>
<div class="kg-callout-text">This is a warning callout about potential issues.</div>
</div>

<h3>Danger Callout</h3>
<div class="kg-card kg-callout-card kg-callout-card-red">
<div class="kg-callout-emoji">❌</div>
<div class="kg-callout-text">This is a danger callout about serious risks.</div>
</div>

<h3>Error Callout</h3>
<div class="kg-card kg-callout-card kg-callout-card-red">
<div class="kg-callout-emoji">🚨</div>
<div class="kg-callout-text">This is an error callout about problems.</div>
</div>

<h3>Question Callout</h3>
<div class="kg-card kg-callout-card kg-callout-card-blue">
<div class="kg-callout-emoji">❓</div>
<div class="kg-callout-text">This is a question callout for inquiries.</div>
</div>

<h3>Quote Callout</h3>
<div class="kg-card kg-callout-card kg-callout-card-grey">
<div class="kg-callout-emoji">💬</div>
<div class="kg-callout-text">This is a quote callout for citations.</div>
</div>

<h3>Example Callout</h3>
<div class="kg-card kg-callout-card kg-callout-card-blue">
<div class="kg-callout-emoji">📋</div>
<div class="kg-callout-text">This is an example callout with sample content.</div>
</div>

<h3>Abstract Callout</h3>
<div class="kg-card kg-callout-card kg-callout-card-blue">
<div class="kg-callout-emoji">📄</div>
<div class="kg-callout-text">This is an abstract callout with summary information.</div>
</div>

<h3>Todo Callout</h3>
<div class="kg-card kg-callout-card kg-callout-card-blue">
<div class="kg-callout-emoji">☑️</div>
<div class="kg-callout-text">This is a todo callout for task tracking.</div>
</div>

<h3>Bug Callout</h3>
<div class="kg-card kg-callout-card kg-callout-card-red">
<div class="kg-callout-emoji">🐛</div>
<div class="kg-callout-text">This is a bug callout for issue reporting.</div>
</div>

<h2>Tables</h2>

<table>
<thead>
<tr>
<th>Feature</th>
<th>Obsidian</th>
<th>Ghost</th>
<th>Sync Status</th>
</tr>
</thead>
<tbody>
<tr>
<td>Text Formatting</td>
<td>✅</td>
<td>✅</td>
<td>✅</td>
</tr>
<tr>
<td>Callouts</td>
<td>✅</td>
<td>✅</td>
<td>✅</td>
</tr>
<tr>
<td>Tables</td>
<td>✅</td>
<td>✅</td>
<td>✅</td>
</tr>
<tr>
<td>Code Blocks</td>
<td>✅</td>
<td>✅</td>
<td>✅</td>
</tr>
</tbody>
</table>

<h2>Code Blocks</h2>

<h3>JavaScript Code</h3>
<pre><code class="language-javascript">function greet(name) {
  console.log(`Hello, ${name}!`);
  return `Welcome to Ghost and Obsidian sync!`;
}

greet('World');
</code></pre>

<h3>Python Code</h3>
<pre><code class="language-python">def calculate_sum(a, b):
    """Calculate the sum of two numbers."""
    result = a + b
    print(f"The sum of {a} and {b} is {result}")
    return result

# Example usage
total = calculate_sum(10, 20)
</code></pre>

<h3>Plain Code Block</h3>
<pre><code>This is a plain code block without syntax highlighting.
It can contain any text or code.
Multiple lines are supported.
</code></pre>

<h2>Links and References</h2>

<p>Here's a regular link: <a href="https://ghost.org">Ghost CMS</a></p>

<p>And here's an Obsidian wikilink: <a href="obsidian://Target Page">Display Text</a></p>

<h2>Conclusion</h2>

<p>This comprehensive test post validates all formatting elements supported by the Obsidian Ghost Sync plugin.</p>
