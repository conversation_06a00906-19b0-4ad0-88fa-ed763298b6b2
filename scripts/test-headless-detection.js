#!/usr/bin/env node

/**
 * Simple script to test headless mode detection logic
 * This verifies that our headless detection works correctly
 */

console.log('🔍 Testing headless mode detection...\n');

// Test different scenarios
const scenarios = [
  {
    name: 'CI environment',
    env: { CI: 'true' },
    expected: true
  },
  {
    name: 'Explicit headless mode',
    env: { E2E_HEADLESS: 'true' },
    expected: true
  },
  {
    name: 'No DISPLAY variable',
    env: { DISPLAY: undefined },
    expected: true
  },
  {
    name: 'Empty DISPLAY variable',
    env: { DISPLAY: '' },
    expected: true
  },
  {
    name: 'Normal windowed mode',
    env: { CI: 'false', E2E_HEADLESS: 'false', DISPLAY: ':0' },
    expected: false
  },
  {
    name: 'Force windowed mode',
    env: { CI: 'true', E2E_HEADLESS: 'false', DISPLAY: ':0' },
    expected: false
  }
];

function testHeadlessDetection(env) {
  // Simulate the detection logic from plugin-setup.ts
  // E2E_HEADLESS=false can override CI mode to force windowed mode
  const isHeadless = env.E2E_HEADLESS === 'false' ? false :
    (env.CI === 'true' ||
      env.E2E_HEADLESS === 'true' ||
      env.DISPLAY === undefined ||
      env.DISPLAY === '');
  return isHeadless;
}

let allPassed = true;

scenarios.forEach((scenario, index) => {
  const result = testHeadlessDetection(scenario.env);
  const passed = result === scenario.expected;

  console.log(`${index + 1}. ${scenario.name}`);
  console.log(`   Environment: ${JSON.stringify(scenario.env)}`);
  console.log(`   Expected: ${scenario.expected ? 'headless' : 'windowed'}`);
  console.log(`   Actual: ${result ? 'headless' : 'windowed'}`);
  console.log(`   Result: ${passed ? '✅ PASS' : '❌ FAIL'}\n`);

  if (!passed) {
    allPassed = false;
  }
});

console.log(`Overall result: ${allPassed ? '✅ All tests passed!' : '❌ Some tests failed!'}`);

// Test current environment
console.log('\n🖥️ Current environment:');
console.log(`   CI: ${process.env.CI || 'undefined'}`);
console.log(`   E2E_HEADLESS: ${process.env.E2E_HEADLESS || 'undefined'}`);
console.log(`   DISPLAY: ${process.env.DISPLAY || 'undefined'}`);

const currentResult = testHeadlessDetection(process.env);
console.log(`   Detected mode: ${currentResult ? 'headless' : 'windowed'}`);

// Test xvfb availability
console.log('\n🔧 System checks:');
const { execSync } = require('child_process');

try {
  execSync('which xvfb-run', { stdio: 'ignore' });
  console.log('   ✅ xvfb-run is available');
} catch (error) {
  console.log('   ❌ xvfb-run is not available');
}

try {
  execSync('which node', { stdio: 'ignore' });
  console.log('   ✅ Node.js is available');
} catch (error) {
  console.log('   ❌ Node.js is not available');
}

try {
  const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
  console.log(`   ✅ Node.js version: ${nodeVersion}`);
} catch (error) {
  console.log('   ❌ Could not get Node.js version');
}

process.exit(allPassed ? 0 : 1);
