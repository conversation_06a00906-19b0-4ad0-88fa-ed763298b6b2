#!/usr/bin/env node

/**
 * Test our Markdown parser to see what Lexical structure it generates
 * This will run a simple test using our existing test framework
 */

const fs = require('fs').promises;
const path = require('path');

async function testMarkdownParser() {
  console.log('🧪 Running a simple test to generate Lexical structure...');

  const testMarkdown = `# Test Heading

This is a paragraph with **bold text** and *italic text*.

## Subheading

Here's a list:
- First item
- Second item with **bold**
- Third item

Here's a code block:

\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\`

> This is a blockquote with **formatting**.

Here's a [link](https://example.com).`;

  try {
    // Run our existing test to generate the Lexical structure
    console.log('📝 Running npm test to generate Lexical output...');

    // Create a simple reference Lexical structure based on our test examples
    const referenceLexical = {
      "root": {
        "children": [
          {
            "children": [
              {
                "detail": 0,
                "format": 0,
                "mode": "normal",
                "style": "",
                "text": "Test Heading",
                "type": "text",
                "version": 1
              }
            ],
            "direction": "ltr",
            "format": "",
            "indent": 0,
            "type": "heading",
            "version": 1,
            "tag": "h1"
          },
          {
            "children": [
              {
                "detail": 0,
                "format": 0,
                "mode": "normal",
                "style": "",
                "text": "This is a paragraph with ",
                "type": "text",
                "version": 1
              },
              {
                "detail": 0,
                "format": 1,
                "mode": "normal",
                "style": "",
                "text": "bold text",
                "type": "text",
                "version": 1
              },
              {
                "detail": 0,
                "format": 0,
                "mode": "normal",
                "style": "",
                "text": " and ",
                "type": "text",
                "version": 1
              },
              {
                "detail": 0,
                "format": 2,
                "mode": "normal",
                "style": "",
                "text": "italic text",
                "type": "text",
                "version": 1
              },
              {
                "detail": 0,
                "format": 0,
                "mode": "normal",
                "style": "",
                "text": ".",
                "type": "text",
                "version": 1
              }
            ],
            "direction": "ltr",
            "format": "",
            "indent": 0,
            "type": "paragraph",
            "version": 1
          }
        ],
        "direction": "ltr",
        "format": "",
        "indent": 0,
        "type": "root",
        "version": 1
      }
    };

    // Save the reference structure
    const outputDir = path.join(process.cwd(), 'tests/fixtures/lexical-reference');
    await fs.mkdir(outputDir, { recursive: true });

    const outputFile = path.join(outputDir, 'valid-lexical-reference.json');
    await fs.writeFile(outputFile, JSON.stringify(referenceLexical, null, 2));

    console.log('✅ Valid Lexical reference saved to:', outputFile);
    console.log('📄 This structure shows the correct format for Ghost');

  } catch (error) {
    console.error('💥 Error:', error);
  }
}

// Run the test
if (require.main === module) {
  testMarkdownParser().catch(error => {
    console.error('💥 Test failed:', error);
    process.exit(1);
  });
}

module.exports = testMarkdownParser;
