#!/usr/bin/env node

/**
 * Manual cleanup script for Ghost test posts
 * Usage: node scripts/cleanup-ghost-posts.mjs [--all] [--dry-run]
 */

import GhostAdminAPI from '@tryghost/admin-api';
import * as fs from 'fs';
import * as path from 'path';

const args = process.argv.slice(2);
const deleteAll = args.includes('--all');
const dryRun = args.includes('--dry-run');

/**
 * Load environment settings from .env file and process.env
 */
function loadEnvironmentSettings() {
  const envSettings = {};

  // Try to read from .env file
  const envPath = path.resolve('.env');
  if (fs.existsSync(envPath)) {
    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');

      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            let value = valueParts.join('=').trim();
            // Remove quotes if present
            if ((value.startsWith('"') && value.endsWith('"')) ||
              (value.startsWith("'") && value.endsWith("'"))) {
              value = value.slice(1, -1);
            }

            if (key.trim() === 'GHOST_URL') {
              envSettings.ghostUrl = value;
            } else if (key.trim() === 'GHOST_ADMIN_API_KEY') {
              envSettings.ghostAdminApiKey = value;
            }
          }
        }
      }
    } catch (error) {
      console.log('⚠️ Could not read .env file:', error.message);
    }
  }

  // Override with process.env if available
  if (process.env.GHOST_URL) {
    envSettings.ghostUrl = process.env.GHOST_URL;
  }
  if (process.env.GHOST_ADMIN_API_KEY) {
    envSettings.ghostAdminApiKey = process.env.GHOST_ADMIN_API_KEY;
  }

  return envSettings;
}

async function cleanupGhostPosts() {
  console.log('🧹 Ghost Posts Cleanup Script');
  console.log('============================');
  
  if (dryRun) {
    console.log('🔍 DRY RUN MODE - No posts will be deleted');
  }
  
  const envSettings = loadEnvironmentSettings();
  
  if (!envSettings.ghostUrl || !envSettings.ghostAdminApiKey) {
    console.error('❌ Ghost credentials not found in environment');
    console.log('Please set GHOST_URL and GHOST_ADMIN_API_KEY in .env file or environment variables');
    process.exit(1);
  }

  console.log('🔗 Connecting to Ghost at:', envSettings.ghostUrl);
  
  const api = new GhostAdminAPI({
    url: envSettings.ghostUrl.replace(/\/$/, ''),
    key: envSettings.ghostAdminApiKey,
    version: 'v6.0'
  });

  try {
    // Get all posts
    console.log('📋 Fetching all posts...');
    const posts = await api.posts.browse({ limit: 'all' });
    
    console.log(`📊 Found ${posts.length} posts total`);
    
    let postsToDelete;
    
    if (deleteAll) {
      postsToDelete = posts;
      console.log('⚠️ ALL POSTS will be deleted!');
    } else {
      // Filter test posts
      postsToDelete = posts.filter(post => 
        post.title?.toLowerCase().includes('test') ||
        post.slug?.toLowerCase().includes('test')
      );
      console.log(`🎯 Found ${postsToDelete.length} test posts`);
    }
    
    if (postsToDelete.length === 0) {
      console.log('✅ No posts found to delete');
      return;
    }

    console.log('\nPosts to delete:');
    postsToDelete.forEach((post, index) => {
      console.log(`  ${index + 1}. ${post.title} (${post.slug}) - ${post.id}`);
    });
    
    if (dryRun) {
      console.log('\n✅ Dry run completed - no posts were deleted');
      return;
    }
    
    console.log(`\n⚠️ This will delete ${postsToDelete.length} posts.`);
    console.log('Press Ctrl+C to cancel, or any key to continue...');
    
    // Wait for user input
    process.stdin.setRawMode(true);
    process.stdin.resume();
    await new Promise(resolve => {
      process.stdin.once('data', () => {
        process.stdin.setRawMode(false);
        process.stdin.pause();
        resolve();
      });
    });
    
    console.log('\n🗑️ Deleting posts...');
    
    let deleted = 0;
    let failed = 0;
    
    for (const post of postsToDelete) {
      try {
        await api.posts.delete({ id: post.id });
        console.log(`✅ Deleted: ${post.title}`);
        deleted++;
      } catch (error) {
        console.error(`❌ Failed to delete ${post.title}:`, error.message);
        failed++;
      }
    }
    
    console.log(`\n📊 Cleanup Summary:`);
    console.log(`  ✅ Deleted: ${deleted} posts`);
    console.log(`  ❌ Failed: ${failed} posts`);
    console.log('✅ Cleanup completed');
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  }
}

// Show usage if help requested
if (args.includes('--help') || args.includes('-h')) {
  console.log('Ghost Posts Cleanup Script');
  console.log('Usage: node scripts/cleanup-ghost-posts.mjs [options]');
  console.log('');
  console.log('Options:');
  console.log('  --all      Delete ALL posts (not just test posts)');
  console.log('  --dry-run  Show what would be deleted without actually deleting');
  console.log('  --help     Show this help message');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/cleanup-ghost-posts.mjs --dry-run    # See what would be deleted');
  console.log('  node scripts/cleanup-ghost-posts.mjs              # Delete test posts');
  console.log('  node scripts/cleanup-ghost-posts.mjs --all        # Delete ALL posts');
  process.exit(0);
}

cleanupGhostPosts();
