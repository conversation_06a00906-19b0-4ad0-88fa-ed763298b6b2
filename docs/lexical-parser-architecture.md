# Lexical Parser Architecture Design

## Overview

This document outlines the architecture for the bidirectional Obsidian Markdown ↔ Ghost Lexical parser system.

## Current State Analysis

### Integration Points Identified

1. **ContentConverter.createGhostPostData()** - Line 212: Currently generates HTML only
2. **ContentConverter.convertGhostPostToArticle()** - Line 703: Limited lexical extraction
3. **ContentConverter.extractMarkdownFromLexical()** - Line 734: Only handles markdown cards

### Ghost Lexical Format Structure

Based on research and test data, Ghost's Lexical format follows this structure:

```typescript
interface LexicalDocument {
  root: {
    children: LexicalNode[];
    direction?: 'ltr' | 'rtl';
    format?: string;
    indent?: number;
    type: 'root';
    version?: number;
  };
}

interface LexicalNode {
  type: string;
  version?: number;
  [key: string]: any;
}
```

### Known Node Types

From test data and research:

1. **markdown** - Contains raw markdown in `markdown` property
2. **paragraph** - Text paragraphs with children
3. **heading** - Headings with `tag` property (h1-h6)
4. **text** - Text nodes with formatting properties
5. **list** - Lists with `listType` property
6. **listitem** - List items
7. **link** - Links with `url` property
8. **code** - Code blocks
9. **callout** - Ghost callout cards
10. **bookmark** - Ghost bookmark cards
11. **gallery** - Ghost gallery cards
12. **embed** - Ghost embed cards

## Proposed Architecture

### Core Classes

```typescript
// Main parser interface
export class LexicalMarkdownParser {
  static markdownToLexical(markdown: string): LexicalDocument;
  static lexicalToMarkdown(lexicalDoc: LexicalDocument): string;
  
  private static parseMarkdownAST(markdown: string): MarkdownAST;
  private static convertASTToLexical(ast: MarkdownAST): LexicalDocument;
  private static convertLexicalToMarkdown(doc: LexicalDocument): string;
}

// Node conversion system
export abstract class NodeConverter {
  abstract markdownToLexical(node: MarkdownNode): LexicalNode | LexicalNode[];
  abstract lexicalToMarkdown(node: LexicalNode): string;
  abstract canHandle(nodeType: string): boolean;
}

// Registry for node converters
export class ConverterRegistry {
  private static converters: Map<string, NodeConverter> = new Map();
  
  static register(nodeType: string, converter: NodeConverter): void;
  static getConverter(nodeType: string): NodeConverter | null;
  static getAllConverters(): NodeConverter[];
}
```

### Node Converter Implementations

```typescript
export class ParagraphConverter extends NodeConverter {
  canHandle(nodeType: string): boolean { return nodeType === 'paragraph'; }
  markdownToLexical(node: MarkdownNode): LexicalNode;
  lexicalToMarkdown(node: LexicalNode): string;
}

export class HeadingConverter extends NodeConverter {
  canHandle(nodeType: string): boolean { return nodeType === 'heading'; }
  markdownToLexical(node: MarkdownNode): LexicalNode;
  lexicalToMarkdown(node: LexicalNode): string;
}

export class TextConverter extends NodeConverter {
  canHandle(nodeType: string): boolean { return nodeType === 'text'; }
  markdownToLexical(node: MarkdownNode): LexicalNode;
  lexicalToMarkdown(node: LexicalNode): string;
}

export class ListConverter extends NodeConverter {
  canHandle(nodeType: string): boolean { return ['list', 'listitem'].includes(nodeType); }
  markdownToLexical(node: MarkdownNode): LexicalNode;
  lexicalToMarkdown(node: LexicalNode): string;
}

export class LinkConverter extends NodeConverter {
  canHandle(nodeType: string): boolean { return nodeType === 'link'; }
  markdownToLexical(node: MarkdownNode): LexicalNode;
  lexicalToMarkdown(node: LexicalNode): string;
}

export class CodeConverter extends NodeConverter {
  canHandle(nodeType: string): boolean { return ['code', 'codeblock'].includes(nodeType); }
  markdownToLexical(node: MarkdownNode): LexicalNode;
  lexicalToMarkdown(node: LexicalNode): string;
}

// Ghost-specific converters
export class CalloutConverter extends NodeConverter {
  canHandle(nodeType: string): boolean { return nodeType === 'callout'; }
  markdownToLexical(node: MarkdownNode): LexicalNode;
  lexicalToMarkdown(node: LexicalNode): string;
}

export class BookmarkConverter extends NodeConverter {
  canHandle(nodeType: string): boolean { return nodeType === 'bookmark'; }
  markdownToLexical(node: MarkdownNode): LexicalNode;
  lexicalToMarkdown(node: LexicalNode): string;
}
```

### Error Handling and Fallbacks

```typescript
export class FallbackConverter extends NodeConverter {
  canHandle(nodeType: string): boolean { return true; } // Handles all unknown types
  
  markdownToLexical(node: MarkdownNode): LexicalNode {
    // Convert to markdown card as fallback
    return {
      type: 'markdown',
      markdown: this.nodeToMarkdown(node),
      version: 1
    };
  }
  
  lexicalToMarkdown(node: LexicalNode): string {
    // Use HTML conversion as fallback
    return ContentConverter.htmlToMarkdown(this.lexicalToHTML(node));
  }
}
```

### Integration Strategy

1. **Phase 1**: Create parser infrastructure without breaking existing functionality
2. **Phase 2**: Add Lexical generation alongside HTML in `createGhostPostData()`
3. **Phase 3**: Enhance `convertGhostPostToArticle()` to use full Lexical parsing
4. **Phase 4**: Optimize and add advanced features

### Dependencies

- `markdown-it` - Robust markdown parsing with plugin support
- `@types/markdown-it` - TypeScript definitions
- Existing Ghost API and sync infrastructure

### File Structure

```
src/utils/lexical-parser/
├── index.ts                 # Main exports
├── lexical-markdown-parser.ts # Main parser class
├── converter-registry.ts    # Node converter registry
├── types.ts                # TypeScript interfaces
├── converters/
│   ├── paragraph.ts        # Paragraph converter
│   ├── heading.ts          # Heading converter
│   ├── text.ts             # Text converter
│   ├── list.ts             # List converter
│   ├── link.ts             # Link converter
│   ├── code.ts             # Code converter
│   ├── callout.ts          # Callout converter
│   ├── bookmark.ts         # Bookmark converter
│   ├── gallery.ts          # Gallery converter
│   ├── embed.ts            # Embed converter
│   └── fallback.ts         # Fallback converter
└── utils/
    ├── markdown-ast.ts     # Markdown AST utilities
    ├── lexical-utils.ts    # Lexical document utilities
    └── formatting.ts       # Text formatting utilities
```

This architecture provides:
- Extensible node conversion system
- Clear separation of concerns
- Robust error handling with fallbacks
- Easy testing and maintenance
- Future-proof design for new node types
