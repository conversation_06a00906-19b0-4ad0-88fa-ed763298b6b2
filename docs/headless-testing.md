# Headless Testing Setup for Obsidian Ghost Sync

This document provides comprehensive instructions for setting up and running e2e tests in headless mode on Debian/Ubuntu systems.

## Overview

Headless testing allows you to run Electron-based e2e tests without a graphical display, which is essential for:

- **CI/CD environments** (GitHub Actions, Jenkins, etc.)
- **Server environments** without X11 display
- **Docker containers** 
- **Remote development** environments
- **Automated testing** pipelines

## Prerequisites

- Debian/Ubuntu-based system
- Node.js (v18 or higher)
- npm or yarn package manager
- sudo access (for installing system dependencies)

## Quick Setup

### Automatic Setup

Run the automated setup script:

```bash
npm run setup:headless
```

This script will:
- Install Xvfb (X Virtual Framebuffer)
- Install required system dependencies for Electron
- Verify the installation

### Manual Setup

If you prefer manual installation:

```bash
# Update package list
sudo apt-get update

# Install Xvfb and dependencies
sudo apt-get install -y xvfb libnss3 libatk-bridge2.0-0 libdrm2 \
  libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2 \
  libatspi2.0-0 libgtk-3-0

# Verify installation
xvfb-run --help
```

## Running Tests

### Available Commands

```bash
# Run tests in headless mode (recommended for CI)
npm run test:e2e:headless

# Run tests in windowed mode (for development)
npm run test:e2e:windowed

# Run tests with CI optimizations
npm run test:e2e:ci

# Run standard e2e tests (auto-detects mode)
npm run test:e2e
```

### Environment Variables

Control test behavior with environment variables:

| Variable | Values | Description |
|----------|--------|-------------|
| `E2E_HEADLESS` | `true`/`false` | Force headless/windowed mode |
| `CI` | `true`/`false` | Enable CI optimizations |
| `DISPLAY` | `:0`, `:99`, etc. | X11 display to use |

### Examples

```bash
# Force headless mode
E2E_HEADLESS=true npm run test:e2e

# Force windowed mode (even in CI)
E2E_HEADLESS=false npm run test:e2e

# Run with custom display
DISPLAY=:1 npm run test:e2e

# Run with xvfb-run directly
xvfb-run -a --server-args="-screen 0 1280x1024x24 -ac -nolisten tcp -dpi 96" npm run test:e2e
```

## Headless Mode Detection

The system automatically detects when to run in headless mode based on:

1. **Explicit override**: `E2E_HEADLESS=false` forces windowed mode
2. **CI environment**: `CI=true` enables headless mode
3. **Explicit headless**: `E2E_HEADLESS=true` enables headless mode
4. **No display**: Missing or empty `DISPLAY` variable enables headless mode

### Detection Logic

```javascript
const isHeadless = process.env.E2E_HEADLESS === 'false' ? false :
  (process.env.CI === 'true' ||
   process.env.E2E_HEADLESS === 'true' ||
   process.env.DISPLAY === undefined ||
   process.env.DISPLAY === '');
```

## Testing the Setup

### Verify Headless Detection

```bash
npm run test:headless-detection
```

This tests the headless mode detection logic with various scenarios.

### Verify Xvfb Functionality

```bash
npm run test:xvfb
```

This tests that xvfb-run works correctly with Node.js scripts.

## Troubleshooting

### Common Issues

#### 1. "xvfb-run: command not found"

**Solution**: Install Xvfb
```bash
sudo apt-get update
sudo apt-get install -y xvfb
```

#### 2. "Cannot find module @rollup/rollup-linux-arm64-gnu"

**Solution**: Reinstall dependencies
```bash
rm -rf node_modules package-lock.json
npm install
```

#### 3. Electron launch timeout

**Symptoms**: Tests hang or timeout during Electron startup

**Solutions**:
- Increase timeout in `vitest.playwright.config.mjs`
- Check system resources (memory, CPU)
- Try single concurrency: `maxConcurrency: 1`
- Verify Xvfb is running: `ps aux | grep Xvfb`

#### 4. Permission errors

**Symptoms**: Cannot write to test directories

**Solutions**:
- Check file permissions: `ls -la e2e/test-environments/`
- Ensure user has write access to workspace
- Try running with different user

#### 5. Display errors

**Symptoms**: "Cannot open display" errors

**Solutions**:
- Verify DISPLAY variable: `echo $DISPLAY`
- Check Xvfb process: `ps aux | grep Xvfb`
- Try explicit display: `DISPLAY=:99 npm run test:e2e`

### Debug Mode

Enable verbose logging for debugging:

```bash
# Enable Playwright debug logs
DEBUG=pw:* npm run test:e2e:headless

# Enable Vitest debug logs
DEBUG=vitest:* npm run test:e2e:headless

# Enable both
DEBUG=pw:*,vitest:* npm run test:e2e:headless
```

### System Information

Check system compatibility:

```bash
# Check OS version
cat /etc/os-release

# Check available memory
free -h

# Check X11 libraries
dpkg -l | grep -E "(libx|xvfb)"

# Check running processes
ps aux | grep -E "(xvfb|electron)"
```

## CI/CD Integration

### GitHub Actions

The project includes a GitHub Actions workflow (`.github/workflows/e2e-tests.yml`) that:

1. Sets up Ubuntu environment
2. Installs Xvfb and dependencies
3. Downloads and extracts Obsidian
4. Runs tests in headless mode

### Docker

For Docker environments, ensure your Dockerfile includes:

```dockerfile
# Install system dependencies
RUN apt-get update && apt-get install -y \
    xvfb \
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    libatspi2.0-0 \
    libgtk-3-0 \
    && rm -rf /var/lib/apt/lists/*

# Set environment for headless mode
ENV CI=true
ENV DISPLAY=:99
```

## Performance Optimization

### For CI Environments

The configuration automatically optimizes for CI:

- Single concurrency (`maxConcurrency: 1`)
- Longer timeouts (`testTimeout: 120000`)
- Single-threaded execution (`singleThread: true`)
- Verbose reporting

### Memory Usage

Headless mode typically uses less memory than windowed mode:

- **Windowed mode**: ~200-400MB per Electron instance
- **Headless mode**: ~100-200MB per Electron instance

### Xvfb Configuration

The default Xvfb configuration is optimized for testing:

```bash
xvfb-run -a --server-args="-screen 0 1280x1024x24 -ac -nolisten tcp -dpi 96"
```

- `-a`: Automatically select display number
- `-screen 0 1280x1024x24`: Set screen resolution and color depth
- `-ac`: Disable access control
- `-nolisten tcp`: Disable TCP connections
- `-dpi 96`: Set DPI for consistent rendering

## Best Practices

1. **Always test locally** before pushing to CI
2. **Use headless mode for CI** and windowed mode for development
3. **Monitor resource usage** in CI environments
4. **Keep timeouts reasonable** but sufficient for slow environments
5. **Clean up test environments** after each test run
6. **Use single concurrency** in resource-constrained environments

## References

- [Electron Testing on Headless CI](https://www.electronjs.org/docs/latest/tutorial/testing-on-headless-ci)
- [Xvfb Documentation](https://www.x.org/releases/X11R7.6/doc/man/man1/Xvfb.1.xhtml)
- [Playwright Electron Testing](https://playwright.dev/docs/api/class-electron)
- [Vitest Configuration](https://vitest.dev/config/)
