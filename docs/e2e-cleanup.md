# E2E Test Cleanup

## Overview

The e2e tests create posts on a real Ghost instance for testing purposes. To prevent the test account from getting cluttered with test posts, we've implemented automatic cleanup functionality.

## Automatic Cleanup

### Global Teardown
The e2e test suite includes a global teardown that automatically deletes all posts from the Ghost test instance after all tests complete. This is configured in:

- `vitest.playwright.config.mjs` - Configures global teardown
- `e2e/global-teardown.ts` - Implements the cleanup logic
- `e2e/helpers/ghost-cleanup.ts` - Contains the cleanup functions

### Per-Test Cleanup
Individual test suites also perform cleanup in their `afterAll` hooks to remove test posts created during that specific test run.

## Manual Cleanup

You can also manually clean up the Ghost instance using the provided scripts:

### View what would be deleted (dry run)
```bash
npm run cleanup:ghost:dry-run
```

### Delete test posts only
```bash
npm run cleanup:ghost
```

### Delete ALL posts (use with caution!)
```bash
npm run cleanup:ghost:all
```

### Direct script usage
```bash
# Show help
node scripts/cleanup-ghost-posts.mjs --help

# Dry run to see what would be deleted
node scripts/cleanup-ghost-posts.mjs --dry-run

# Delete test posts
node scripts/cleanup-ghost-posts.mjs

# Delete ALL posts (dangerous!)
node scripts/cleanup-ghost-posts.mjs --all
```

## Configuration

The cleanup functionality uses the same Ghost credentials as the tests:

- `GHOST_URL` - The URL of your Ghost test instance
- `GHOST_ADMIN_API_KEY` - The admin API key for your Ghost test instance

These should be set in your `.env` file or as environment variables.

## How It Works

1. **Test Post Identification**: The cleanup identifies test posts by looking for titles or slugs containing "test"
2. **Batch Deletion**: Posts are deleted in batches to avoid overwhelming the Ghost API
3. **Error Handling**: Failed deletions are logged but don't stop the cleanup process
4. **Safety Features**: 
   - Dry run mode to preview what would be deleted
   - User confirmation before actual deletion
   - Separate commands for test posts vs all posts

## Troubleshooting

### Cleanup fails with "obsidian" package error
This is expected in the e2e test context. The cleanup helper uses a simplified Ghost API client that doesn't depend on Obsidian modules.

### No posts found to delete
This means either:
- No test posts exist (good!)
- The Ghost credentials are incorrect
- The Ghost instance is not accessible

### Some posts fail to delete
This can happen if:
- Posts are currently being edited
- Network issues occur
- API rate limits are hit

The cleanup will continue with other posts and report the failures at the end.
