# Installation Guide for Ghost Sync Plugin

## Prerequisites

1. **Working ghost.js script**: Make sure your `scripts/ghost.js` is working correctly
2. **Ghost.io configuration**: Ensure `config/ghost.yml` is properly configured
3. **Node.js**: Must be installed and accessible from your vault directory

## Installation Steps

### Option 1: Manual Installation (Recommended for development)

1. **Create plugin directory**:
   ```bash
   mkdir -p /path/to/your/vault/.obsidian/plugins/ghost-sync
   ```

2. **Copy plugin files**:
   ```bash
   cp obsidian-ghost-sync/* /path/to/your/vault/.obsidian/plugins/ghost-sync/
   ```

3. **Install dependencies** (in the plugin directory):
   ```bash
   cd /path/to/your/vault/.obsidian/plugins/ghost-sync
   npm install
   ```

4. **Build the plugin**:
   ```bash
   npm run build
   ```

5. **Enable the plugin**:
   - Open Obsidian
   - Go to Settings → Community Plugins
   - Turn off "Safe mode" if it's on
   - Find "Ghost Sync" in the installed plugins list
   - Enable it

### Option 2: Development Mode

If you want to develop or modify the plugin:

1. **Clone/copy to plugin directory**
2. **Install dependencies**:
   ```bash
   npm install
   ```
3. **Start development mode**:
   ```bash
   npm run dev
   ```
4. **Enable plugin in Obsidian**

## Configuration

After installation, configure the plugin:

1. Go to Settings → Ghost Sync
2. Set the following:
   - **Ghost script path**: `scripts/ghost.js` (or your actual path)
   - **Articles directory**: `articles` (or your actual directory)
   - **Verbose output**: Enable for debugging

## Testing

1. **Test sync from Ghost**:
   - Open Command Palette (Ctrl/Cmd + P)
   - Type "Sync post from Ghost to local"
   - Enter a post title that exists in your Ghost blog
   - Check if the file appears in your articles directory

2. **Test sync to Ghost**:
   - Open a file in your articles directory
   - Make sure it has proper frontmatter with a title
   - Click the sync icon in the ribbon or use Command Palette
   - Check your Ghost admin to see if the post was updated

## Troubleshooting

### Plugin not appearing
- Make sure all files are in the correct directory
- Check that `manifest.json` is present
- Restart Obsidian

### Sync errors
- Check the console (Ctrl/Cmd + Shift + I) for detailed errors
- Verify your `ghost.js` script works independently
- Ensure Ghost configuration is correct
- Make sure Node.js is accessible from your vault directory

### Permission errors
- Ensure the plugin has permission to execute Node.js commands
- Check file permissions in your vault directory

## File Structure

After installation, your plugin directory should look like:

```
.obsidian/plugins/ghost-sync/
├── main.js (built file)
├── manifest.json
├── styles.css
├── package.json
├── tsconfig.json
├── esbuild.config.mjs
├── version-bump.mjs
├── versions.json
├── main.ts (source)
└── README.md
```
